using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker.Services;

public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configFilePath;
    private readonly string _providersFilePath;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _configFilePath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
        _providersFilePath = Path.Combine(Directory.GetCurrentDirectory(), "api-providers.json");
    }

    public async Task<List<ApiProvider>> GetApiProvidersAsync()
    {
        try
        {
            if (!File.Exists(_providersFilePath))
            {
                // Create default providers
                var defaultProviders = CreateDefaultProviders();
                await SaveApiProvidersAsync(defaultProviders);
                return defaultProviders;
            }

            var json = await File.ReadAllTextAsync(_providersFilePath);
            var providers = JsonConvert.DeserializeObject<List<ApiProvider>>(json) ?? new List<ApiProvider>();
            
            _logger.LogDebug("Loaded {Count} API providers from configuration", providers.Count);
            return providers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading API providers");
            return CreateDefaultProviders();
        }
    }

    public async Task SaveApiProvidersAsync(List<ApiProvider> providers)
    {
        try
        {
            var json = JsonConvert.SerializeObject(providers, Formatting.Indented);
            await File.WriteAllTextAsync(_providersFilePath, json);
            _logger.LogInformation("Saved {Count} API providers to configuration", providers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving API providers");
            throw;
        }
    }

    public Task<GoogleApiSettings> GetGoogleApiSettingsAsync()
    {
        try
        {
            var settings = new GoogleApiSettings
            {
                ApiKey = _configuration["GooglePlacesApi:ApiKey"] ?? string.Empty,
                IsEnabled = !string.IsNullOrWhiteSpace(_configuration["GooglePlacesApi:ApiKey"]) &&
                           _configuration["GooglePlacesApi:ApiKey"] != "YOUR_GOOGLE_PLACES_API_KEY_HERE",
                BaseUrl = _configuration["GooglePlacesApi:BaseUrl"] ?? "https://maps.googleapis.com/maps/api/place",
                AutocompleteEndpoint = _configuration["GooglePlacesApi:AutocompleteEndpoint"] ?? "/autocomplete/json",
                CountryRestriction = _configuration["GooglePlacesApi:CountryRestriction"] ?? "za",
                PlaceTypes = _configuration["GooglePlacesApi:PlaceTypes"] ?? "address"
            };

            return Task.FromResult(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading Google API settings");
            return Task.FromResult(new GoogleApiSettings());
        }
    }

    public async Task SaveGoogleApiSettingsAsync(GoogleApiSettings settings)
    {
        try
        {
            await UpdateAppSettingsAsync(config =>
            {
                config["GooglePlacesApi:ApiKey"] = settings.ApiKey;
                config["GooglePlacesApi:BaseUrl"] = settings.BaseUrl;
                config["GooglePlacesApi:AutocompleteEndpoint"] = settings.AutocompleteEndpoint;
                config["GooglePlacesApi:CountryRestriction"] = settings.CountryRestriction;
                config["GooglePlacesApi:PlaceTypes"] = settings.PlaceTypes;
            });

            _logger.LogInformation("Saved Google API settings");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving Google API settings");
            throw;
        }
    }

    public Task<AppSettings> GetAppSettingsAsync()
    {
        try
        {
            var settings = new AppSettings
            {
                UseDemo = ParseBool(_configuration["OctopiApi:UseDemo"], true),
                DefaultApiProviderId = _configuration["OctopiApi:DefaultProviderId"] ?? string.Empty,
                CacheExpirationMinutes = ParseInt(_configuration["Cache:ExpirationMinutes"], 60),
                MaxCacheItems = ParseInt(_configuration["Cache:MaxItems"], 1000),
                ExportDirectory = _configuration["Export:DefaultDirectory"] ?? "exports",
                DateFormat = _configuration["Export:DateFormat"] ?? "yyyy-MM-dd_HH-mm-ss"
            };

            return Task.FromResult(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading app settings");
            return Task.FromResult(new AppSettings());
        }
    }

    public async Task SaveAppSettingsAsync(AppSettings settings)
    {
        try
        {
            await UpdateAppSettingsAsync(config =>
            {
                config["OctopiApi:UseDemo"] = settings.UseDemo.ToString().ToLower();
                config["OctopiApi:DefaultProviderId"] = settings.DefaultApiProviderId;
                config["Cache:ExpirationMinutes"] = settings.CacheExpirationMinutes.ToString();
                config["Cache:MaxItems"] = settings.MaxCacheItems.ToString();
                config["Export:DefaultDirectory"] = settings.ExportDirectory;
                config["Export:DateFormat"] = settings.DateFormat;
            });

            _logger.LogInformation("Saved app settings");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving app settings");
            throw;
        }
    }

    public Task ReloadConfigurationAsync()
    {
        try
        {
            // Force configuration reload
            if (_configuration is IConfigurationRoot configRoot)
            {
                configRoot.Reload();
            }
            _logger.LogInformation("Configuration reloaded");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reloading configuration");
        }
        return Task.CompletedTask;
    }

    private List<ApiProvider> CreateDefaultProviders()
    {
        return new List<ApiProvider>
        {
            new ApiProvider
            {
                Name = "28East Octopi Portal",
                BaseUrl = "https://octopi.28east.co.za",
                FeasibilityEndpoint = "/api/feasibility",
                IsDefault = true,
                RequestBodyTemplate = "{\"address\":\"{ADDRESS}\"}",
                FeasibleField = "feasible",
                FnoField = "fno",
                ProviderField = "provider",
                MessageField = "message",
                ErrorField = "error"
            },
            new ApiProvider
            {
                Name = "Demo Provider",
                BaseUrl = "demo://localhost",
                FeasibilityEndpoint = "/demo",
                IsEnabled = false,
                RequestBodyTemplate = "{\"address\":\"{ADDRESS}\"}",
                FeasibleField = "feasible",
                FnoField = "fno",
                ProviderField = "provider",
                MessageField = "message",
                ErrorField = "error"
            }
        };
    }

    private bool ParseBool(string? value, bool defaultValue)
    {
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        if (bool.TryParse(value, out bool result))
            return result;

        return defaultValue;
    }

    private int ParseInt(string? value, int defaultValue)
    {
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        if (int.TryParse(value, out int result))
            return result;

        return defaultValue;
    }

    private async Task UpdateAppSettingsAsync(Action<Dictionary<string, string>> updateAction)
    {
        try
        {
            string json;
            if (File.Exists(_configFilePath))
            {
                json = await File.ReadAllTextAsync(_configFilePath);
            }
            else
            {
                json = "{}";
            }

            // Use more robust JSON handling
            Dictionary<string, object> config;
            try
            {
                config = JsonConvert.DeserializeObject<Dictionary<string, object>>(json) ?? new Dictionary<string, object>();
            }
            catch (JsonException)
            {
                // If JSON is invalid, start with empty config
                config = new Dictionary<string, object>();
            }

            var flatConfig = FlattenConfig(config);

            updateAction(flatConfig);

            var nestedConfig = UnflattenConfig(flatConfig);
            var updatedJson = JsonConvert.SerializeObject(nestedConfig, Formatting.Indented);

            await File.WriteAllTextAsync(_configFilePath, updatedJson);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating app settings file");
            throw;
        }
    }

    private Dictionary<string, string> FlattenConfig(Dictionary<string, object> config, string prefix = "")
    {
        var result = new Dictionary<string, string>();

        foreach (var kvp in config)
        {
            var key = string.IsNullOrEmpty(prefix) ? kvp.Key : $"{prefix}:{kvp.Key}";

            // Handle different JSON object types
            if (kvp.Value is Newtonsoft.Json.Linq.JObject jObject)
            {
                var nested = jObject.ToObject<Dictionary<string, object>>() ?? new Dictionary<string, object>();
                var flattened = FlattenConfig(nested, key);
                foreach (var item in flattened)
                {
                    result[item.Key] = item.Value;
                }
            }
            else if (kvp.Value is Dictionary<string, object> nested)
            {
                var flattened = FlattenConfig(nested, key);
                foreach (var item in flattened)
                {
                    result[item.Key] = item.Value;
                }
            }
            else
            {
                result[key] = kvp.Value?.ToString() ?? string.Empty;
            }
        }

        return result;
    }

    private Dictionary<string, object> UnflattenConfig(Dictionary<string, string> flatConfig)
    {
        var result = new Dictionary<string, object>();
        
        foreach (var kvp in flatConfig)
        {
            var keys = kvp.Key.Split(':');
            var current = result;
            
            for (int i = 0; i < keys.Length - 1; i++)
            {
                if (!current.ContainsKey(keys[i]))
                {
                    current[keys[i]] = new Dictionary<string, object>();
                }
                current = (Dictionary<string, object>)current[keys[i]];
            }
            
            current[keys.Last()] = kvp.Value;
        }
        
        return result;
    }
}
