using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OctopiFeasibilityChecker.Models;
using System.Diagnostics;
using System.Net;
using System.Text;

namespace OctopiFeasibilityChecker.Services;

public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configFilePath;
    private readonly string _providersFilePath;
    private readonly HttpClient _httpClient;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger, HttpClient httpClient)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
        _configFilePath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
        _providersFilePath = Path.Combine(Directory.GetCurrentDirectory(), "api-providers.json");
    }

    public async Task<List<ApiProvider>> GetApiProvidersAsync()
    {
        try
        {
            if (!File.Exists(_providersFilePath))
            {
                // Create default providers
                var defaultProviders = CreateDefaultProviders();
                await SaveApiProvidersAsync(defaultProviders);
                return defaultProviders;
            }

            var json = await File.ReadAllTextAsync(_providersFilePath);
            var providers = JsonConvert.DeserializeObject<List<ApiProvider>>(json) ?? new List<ApiProvider>();
            
            _logger.LogDebug("Loaded {Count} API providers from configuration", providers.Count);
            return providers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading API providers");
            return CreateDefaultProviders();
        }
    }

    public async Task SaveApiProvidersAsync(List<ApiProvider> providers)
    {
        try
        {
            var json = JsonConvert.SerializeObject(providers, Formatting.Indented);
            await File.WriteAllTextAsync(_providersFilePath, json);
            _logger.LogInformation("Saved {Count} API providers to configuration", providers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving API providers");
            throw;
        }
    }

    public Task<GoogleApiSettings> GetGoogleApiSettingsAsync()
    {
        try
        {
            var settings = new GoogleApiSettings
            {
                ApiKey = _configuration["GooglePlacesApi:ApiKey"] ?? string.Empty,
                IsEnabled = !string.IsNullOrWhiteSpace(_configuration["GooglePlacesApi:ApiKey"]) &&
                           _configuration["GooglePlacesApi:ApiKey"] != "YOUR_GOOGLE_PLACES_API_KEY_HERE",
                BaseUrl = _configuration["GooglePlacesApi:BaseUrl"] ?? "https://maps.googleapis.com/maps/api/place",
                AutocompleteEndpoint = _configuration["GooglePlacesApi:AutocompleteEndpoint"] ?? "/autocomplete/json",
                CountryRestriction = _configuration["GooglePlacesApi:CountryRestriction"] ?? "za",
                PlaceTypes = _configuration["GooglePlacesApi:PlaceTypes"] ?? "address"
            };

            return Task.FromResult(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading Google API settings");
            return Task.FromResult(new GoogleApiSettings());
        }
    }

    public async Task SaveGoogleApiSettingsAsync(GoogleApiSettings settings)
    {
        try
        {
            await UpdateAppSettingsAsync(config =>
            {
                config["GooglePlacesApi:ApiKey"] = settings.ApiKey;
                config["GooglePlacesApi:BaseUrl"] = settings.BaseUrl;
                config["GooglePlacesApi:AutocompleteEndpoint"] = settings.AutocompleteEndpoint;
                config["GooglePlacesApi:CountryRestriction"] = settings.CountryRestriction;
                config["GooglePlacesApi:PlaceTypes"] = settings.PlaceTypes;
            });

            _logger.LogInformation("Saved Google API settings");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving Google API settings");
            throw;
        }
    }

    public Task<AppSettings> GetAppSettingsAsync()
    {
        try
        {
            var settings = new AppSettings
            {
                UseDemo = ParseBool(_configuration["OctopiApi:UseDemo"], true),
                DefaultApiProviderId = _configuration["OctopiApi:DefaultProviderId"] ?? string.Empty,
                CacheExpirationMinutes = ParseInt(_configuration["Cache:ExpirationMinutes"], 60),
                MaxCacheItems = ParseInt(_configuration["Cache:MaxItems"], 1000),
                ExportDirectory = _configuration["Export:DefaultDirectory"] ?? "exports",
                DateFormat = _configuration["Export:DateFormat"] ?? "yyyy-MM-dd_HH-mm-ss"
            };

            return Task.FromResult(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading app settings");
            return Task.FromResult(new AppSettings());
        }
    }

    public async Task SaveAppSettingsAsync(AppSettings settings)
    {
        try
        {
            await UpdateAppSettingsAsync(config =>
            {
                config["OctopiApi:UseDemo"] = settings.UseDemo.ToString().ToLower();
                config["OctopiApi:DefaultProviderId"] = settings.DefaultApiProviderId;
                config["Cache:ExpirationMinutes"] = settings.CacheExpirationMinutes.ToString();
                config["Cache:MaxItems"] = settings.MaxCacheItems.ToString();
                config["Export:DefaultDirectory"] = settings.ExportDirectory;
                config["Export:DateFormat"] = settings.DateFormat;
            });

            _logger.LogInformation("Saved app settings");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving app settings");
            throw;
        }
    }

    public Task ReloadConfigurationAsync()
    {
        try
        {
            // Force configuration reload
            if (_configuration is IConfigurationRoot configRoot)
            {
                configRoot.Reload();
            }
            _logger.LogInformation("Configuration reloaded");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reloading configuration");
        }
        return Task.CompletedTask;
    }

    public Task<AutocompleteSettings> GetAutocompleteSettingsAsync()
    {
        try
        {
            var settings = new AutocompleteSettings
            {
                PreferredProvider = ParseEnum<AutocompleteProvider>(_configuration["Autocomplete:PreferredProvider"], AutocompleteProvider.Google),
                UseFallback = ParseBool(_configuration["Autocomplete:UseFallback"], true),
                MinCharacters = ParseInt(_configuration["Autocomplete:MinCharacters"], 3),
                DelayMs = ParseInt(_configuration["Autocomplete:DelayMs"], 500),

                Google = new GoogleApiSettings
                {
                    ApiKey = _configuration["GooglePlacesApi:ApiKey"] ?? string.Empty,
                    IsEnabled = ParseBool(_configuration["GooglePlacesApi:IsEnabled"], false),
                    BaseUrl = _configuration["GooglePlacesApi:BaseUrl"] ?? "https://maps.googleapis.com/maps/api/place",
                    AutocompleteEndpoint = _configuration["GooglePlacesApi:AutocompleteEndpoint"] ?? "/autocomplete/json",
                    CountryRestriction = _configuration["GooglePlacesApi:CountryRestriction"] ?? "za",
                    PlaceTypes = _configuration["GooglePlacesApi:PlaceTypes"] ?? "address"
                },

                Radar = new RadarApiSettings
                {
                    ApiKey = _configuration["RadarApi:ApiKey"] ?? string.Empty,
                    IsEnabled = ParseBool(_configuration["RadarApi:IsEnabled"], false),
                    BaseUrl = _configuration["RadarApi:BaseUrl"] ?? "https://api.radar.io/v1",
                    AutocompleteEndpoint = _configuration["RadarApi:AutocompleteEndpoint"] ?? "/search/autocomplete",
                    CountryRestriction = _configuration["RadarApi:CountryRestriction"] ?? "ZA",
                    MaxResults = ParseInt(_configuration["RadarApi:MaxResults"], 10)
                }
            };

            // Auto-enable if API key is configured
            if (!string.IsNullOrWhiteSpace(settings.Google.ApiKey) && settings.Google.ApiKey != "YOUR_GOOGLE_PLACES_API_KEY_HERE")
            {
                settings.Google.IsEnabled = true;
            }

            if (!string.IsNullOrWhiteSpace(settings.Radar.ApiKey) && settings.Radar.ApiKey != "YOUR_RADAR_API_KEY_HERE")
            {
                settings.Radar.IsEnabled = true;
            }

            return Task.FromResult(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading autocomplete settings");
            return Task.FromResult(new AutocompleteSettings());
        }
    }

    public async Task SaveAutocompleteSettingsAsync(AutocompleteSettings settings)
    {
        try
        {
            await UpdateAppSettingsAsync(config =>
            {
                // General autocomplete settings
                config["Autocomplete:PreferredProvider"] = settings.PreferredProvider.ToString();
                config["Autocomplete:UseFallback"] = settings.UseFallback.ToString().ToLower();
                config["Autocomplete:MinCharacters"] = settings.MinCharacters.ToString();
                config["Autocomplete:DelayMs"] = settings.DelayMs.ToString();

                // Google settings
                config["GooglePlacesApi:ApiKey"] = settings.Google.ApiKey;
                config["GooglePlacesApi:IsEnabled"] = settings.Google.IsEnabled.ToString().ToLower();
                config["GooglePlacesApi:BaseUrl"] = settings.Google.BaseUrl;
                config["GooglePlacesApi:AutocompleteEndpoint"] = settings.Google.AutocompleteEndpoint;
                config["GooglePlacesApi:CountryRestriction"] = settings.Google.CountryRestriction;
                config["GooglePlacesApi:PlaceTypes"] = settings.Google.PlaceTypes;

                // Radar settings
                config["RadarApi:ApiKey"] = settings.Radar.ApiKey;
                config["RadarApi:IsEnabled"] = settings.Radar.IsEnabled.ToString().ToLower();
                config["RadarApi:BaseUrl"] = settings.Radar.BaseUrl;
                config["RadarApi:AutocompleteEndpoint"] = settings.Radar.AutocompleteEndpoint;
                config["RadarApi:CountryRestriction"] = settings.Radar.CountryRestriction;
                config["RadarApi:MaxResults"] = settings.Radar.MaxResults.ToString();
            });

            _logger.LogInformation("Saved autocomplete settings");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving autocomplete settings");
            throw;
        }
    }

    public async Task<ApiTestResult> TestApiProviderAsync(ApiProvider provider, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ApiTestResult();

        try
        {
            // Validate provider configuration
            if (string.IsNullOrWhiteSpace(provider.BaseUrl))
            {
                return new ApiTestResult
                {
                    IsSuccessful = false,
                    Message = "Base URL is required",
                    ErrorDetails = "Provider configuration is incomplete"
                };
            }

            if (string.IsNullOrWhiteSpace(provider.FeasibilityEndpoint))
            {
                return new ApiTestResult
                {
                    IsSuccessful = false,
                    Message = "Feasibility endpoint is required",
                    ErrorDetails = "Provider configuration is incomplete"
                };
            }

            // Construct full URL
            var baseUri = new Uri(provider.BaseUrl);
            var fullUrl = new Uri(baseUri, provider.FeasibilityEndpoint).ToString();

            // Prepare test address
            var testAddress = "123 Test Street, Test City, 1234";
            var requestBody = provider.RequestBodyTemplate.Replace("{ADDRESS}", testAddress);

            // Create HTTP request
            using var request = new HttpRequestMessage();
            request.Method = new HttpMethod(provider.RequestMethod);
            request.RequestUri = new Uri(fullUrl);

            // Set content type
            if (!string.IsNullOrWhiteSpace(provider.ContentType))
            {
                request.Content = new StringContent(requestBody, Encoding.UTF8, provider.ContentType);
            }

            // Add authentication headers
            if (!string.IsNullOrWhiteSpace(provider.ApiKey))
            {
                if (!string.IsNullOrWhiteSpace(provider.AuthHeader))
                {
                    request.Headers.Add(provider.AuthHeader, provider.ApiKey);
                }
                else
                {
                    // Default to Authorization header
                    request.Headers.Add("Authorization", $"Bearer {provider.ApiKey}");
                }
            }

            // Set timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(provider.TimeoutSeconds));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            _logger.LogInformation("Testing API provider: {Name} at {Url}", provider.Name, fullUrl);

            // Make the request
            using var response = await _httpClient.SendAsync(request, combinedCts.Token);

            stopwatch.Stop();
            result.ResponseTimeMs = (int)stopwatch.ElapsedMilliseconds;
            result.StatusCode = (int)response.StatusCode;

            // Read response content
            var responseContent = await response.Content.ReadAsStringAsync(combinedCts.Token);
            result.ResponseContent = responseContent;

            // Analyze response
            if (response.IsSuccessStatusCode)
            {
                // Try to parse as JSON and validate structure
                try
                {
                    var jsonResponse = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseContent);

                    if (jsonResponse != null)
                    {
                        // Check if expected fields exist
                        var hasExpectedFields = CheckExpectedFields(jsonResponse, provider);

                        if (hasExpectedFields)
                        {
                            result.IsSuccessful = true;
                            result.Message = $"API responded successfully with valid JSON structure";
                        }
                        else
                        {
                            result.IsSuccessful = false;
                            result.Message = "API responded but JSON structure doesn't match expected fields";
                            result.ErrorDetails = $"Expected fields: {provider.FeasibleField}, {provider.FnoField}, {provider.ProviderField}";
                        }
                    }
                    else
                    {
                        result.IsSuccessful = false;
                        result.Message = "API responded but returned empty JSON";
                    }
                }
                catch (JsonException jsonEx)
                {
                    result.IsSuccessful = false;
                    result.Message = "API responded but returned invalid JSON";
                    result.ErrorDetails = jsonEx.Message;
                }
            }
            else
            {
                result.IsSuccessful = false;
                result.Message = $"API returned error status: {response.StatusCode} {response.ReasonPhrase}";
                result.ErrorDetails = responseContent;
            }

            // Update provider test results
            provider.LastTestedAt = DateTime.Now;
            provider.LastTestSuccessful = result.IsSuccessful;
            provider.LastTestError = result.IsSuccessful ? null : result.Message;

        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || cancellationToken.IsCancellationRequested)
        {
            stopwatch.Stop();
            result.ResponseTimeMs = (int)stopwatch.ElapsedMilliseconds;
            result.IsSuccessful = false;
            result.Message = "Request timed out";
            result.ErrorDetails = $"Request exceeded {provider.TimeoutSeconds} second timeout";

            provider.LastTestedAt = DateTime.Now;
            provider.LastTestSuccessful = false;
            provider.LastTestError = result.Message;
        }
        catch (HttpRequestException httpEx)
        {
            stopwatch.Stop();
            result.ResponseTimeMs = (int)stopwatch.ElapsedMilliseconds;
            result.IsSuccessful = false;
            result.Message = "Network error occurred";
            result.ErrorDetails = httpEx.Message;

            provider.LastTestedAt = DateTime.Now;
            provider.LastTestSuccessful = false;
            provider.LastTestError = result.Message;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.ResponseTimeMs = (int)stopwatch.ElapsedMilliseconds;
            result.IsSuccessful = false;
            result.Message = "Unexpected error occurred";
            result.ErrorDetails = ex.Message;

            provider.LastTestedAt = DateTime.Now;
            provider.LastTestSuccessful = false;
            provider.LastTestError = result.Message;
        }

        _logger.LogInformation("API test completed for {Name}: {Result}", provider.Name, result.GetSummary());
        return result;
    }

    private bool CheckExpectedFields(Dictionary<string, object> jsonResponse, ApiProvider provider)
    {
        // Check if the response contains the expected field names
        var expectedFields = new[]
        {
            provider.FeasibleField,
            provider.FnoField,
            provider.ProviderField
        }.Where(f => !string.IsNullOrWhiteSpace(f));

        // At least one expected field should exist
        return expectedFields.Any(field => jsonResponse.ContainsKey(field));
    }

    private List<ApiProvider> CreateDefaultProviders()
    {
        return new List<ApiProvider>
        {
            new ApiProvider
            {
                Name = "28East Octopi Portal",
                BaseUrl = "https://octopi.28east.co.za",
                FeasibilityEndpoint = "/api/feasibility",
                IsDefault = true,
                RequestBodyTemplate = "{\"address\":\"{ADDRESS}\"}",
                FeasibleField = "feasible",
                FnoField = "fno",
                ProviderField = "provider",
                MessageField = "message",
                ErrorField = "error"
            },
            new ApiProvider
            {
                Name = "Demo Provider",
                BaseUrl = "demo://localhost",
                FeasibilityEndpoint = "/demo",
                IsEnabled = false,
                RequestBodyTemplate = "{\"address\":\"{ADDRESS}\"}",
                FeasibleField = "feasible",
                FnoField = "fno",
                ProviderField = "provider",
                MessageField = "message",
                ErrorField = "error"
            }
        };
    }

    private bool ParseBool(string? value, bool defaultValue)
    {
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        if (bool.TryParse(value, out bool result))
            return result;

        return defaultValue;
    }

    private int ParseInt(string? value, int defaultValue)
    {
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        if (int.TryParse(value, out int result))
            return result;

        return defaultValue;
    }

    private T ParseEnum<T>(string? value, T defaultValue) where T : struct, Enum
    {
        if (string.IsNullOrWhiteSpace(value))
            return defaultValue;

        if (Enum.TryParse<T>(value, true, out T result))
            return result;

        return defaultValue;
    }

    private async Task UpdateAppSettingsAsync(Action<Dictionary<string, string>> updateAction)
    {
        try
        {
            string json;
            if (File.Exists(_configFilePath))
            {
                json = await File.ReadAllTextAsync(_configFilePath);
            }
            else
            {
                json = "{}";
            }

            // Use more robust JSON handling
            Dictionary<string, object> config;
            try
            {
                config = JsonConvert.DeserializeObject<Dictionary<string, object>>(json) ?? new Dictionary<string, object>();
            }
            catch (JsonException)
            {
                // If JSON is invalid, start with empty config
                config = new Dictionary<string, object>();
            }

            var flatConfig = FlattenConfig(config);

            updateAction(flatConfig);

            var nestedConfig = UnflattenConfig(flatConfig);
            var updatedJson = JsonConvert.SerializeObject(nestedConfig, Formatting.Indented);

            await File.WriteAllTextAsync(_configFilePath, updatedJson);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating app settings file");
            throw;
        }
    }

    private Dictionary<string, string> FlattenConfig(Dictionary<string, object> config, string prefix = "")
    {
        var result = new Dictionary<string, string>();

        foreach (var kvp in config)
        {
            var key = string.IsNullOrEmpty(prefix) ? kvp.Key : $"{prefix}:{kvp.Key}";

            // Handle different JSON object types
            if (kvp.Value is Newtonsoft.Json.Linq.JObject jObject)
            {
                var nested = jObject.ToObject<Dictionary<string, object>>() ?? new Dictionary<string, object>();
                var flattened = FlattenConfig(nested, key);
                foreach (var item in flattened)
                {
                    result[item.Key] = item.Value;
                }
            }
            else if (kvp.Value is Dictionary<string, object> nested)
            {
                var flattened = FlattenConfig(nested, key);
                foreach (var item in flattened)
                {
                    result[item.Key] = item.Value;
                }
            }
            else
            {
                result[key] = kvp.Value?.ToString() ?? string.Empty;
            }
        }

        return result;
    }

    private Dictionary<string, object> UnflattenConfig(Dictionary<string, string> flatConfig)
    {
        var result = new Dictionary<string, object>();
        
        foreach (var kvp in flatConfig)
        {
            var keys = kvp.Key.Split(':');
            var current = result;
            
            for (int i = 0; i < keys.Length - 1; i++)
            {
                if (!current.ContainsKey(keys[i]))
                {
                    current[keys[i]] = new Dictionary<string, object>();
                }
                current = (Dictionary<string, object>)current[keys[i]];
            }
            
            current[keys.Last()] = kvp.Value;
        }
        
        return result;
    }
}
