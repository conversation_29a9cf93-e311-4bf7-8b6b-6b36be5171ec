using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace OctopiFeasibilityChecker.Services;

public class CacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IConfiguration _configuration;
    private readonly ILogger<CacheService> _logger;
    private readonly TimeSpan _defaultExpiration;
    private readonly int _maxItems;
    private int _currentItemCount = 0;

    public CacheService(
        IMemoryCache memoryCache,
        IConfiguration configuration,
        ILogger<CacheService> logger)
    {
        _memoryCache = memoryCache;
        _configuration = configuration;
        _logger = logger;

        var expirationMinutes = int.Parse(_configuration["Cache:ExpirationMinutes"] ?? "60");
        _defaultExpiration = TimeSpan.FromMinutes(expirationMinutes);
        _maxItems = int.Parse(_configuration["Cache:MaxItems"] ?? "1000");
    }

    public Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            var cacheKey = GetCacheKey<T>(key);
            var result = _memoryCache.Get<T>(cacheKey);
            
            if (result != null)
            {
                _logger.LogDebug("Cache hit for key: {Key}", cacheKey);
            }
            else
            {
                _logger.LogDebug("Cache miss for key: {Key}", cacheKey);
            }

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving from cache for key: {Key}", key);
            return Task.FromResult<T?>(null);
        }
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
    {
        try
        {
            if (_currentItemCount >= _maxItems)
            {
                _logger.LogWarning("Cache is at maximum capacity ({MaxItems}). Consider clearing old entries.", _maxItems);
                return Task.CompletedTask;
            }

            var cacheKey = GetCacheKey<T>(key);
            var cacheExpiration = expiration ?? _defaultExpiration;

            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = cacheExpiration,
                Priority = CacheItemPriority.Normal
            };

            cacheEntryOptions.RegisterPostEvictionCallback((evictedKey, evictedValue, reason, state) =>
            {
                Interlocked.Decrement(ref _currentItemCount);
                _logger.LogDebug("Cache entry evicted: {Key}, Reason: {Reason}", evictedKey, reason);
            });

            _memoryCache.Set(cacheKey, value, cacheEntryOptions);
            Interlocked.Increment(ref _currentItemCount);

            _logger.LogDebug("Cached item with key: {Key}, Expiration: {Expiration}", cacheKey, cacheExpiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache for key: {Key}", key);
        }

        return Task.CompletedTask;
    }

    public Task RemoveAsync(string key)
    {
        try
        {
            var cacheKey = GetCacheKey<object>(key);
            _memoryCache.Remove(cacheKey);
            Interlocked.Decrement(ref _currentItemCount);
            _logger.LogDebug("Removed cache entry for key: {Key}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing from cache for key: {Key}", key);
        }

        return Task.CompletedTask;
    }

    public Task ClearAsync()
    {
        try
        {
            if (_memoryCache is MemoryCache mc)
            {
                mc.Compact(1.0); // Remove all entries
                _currentItemCount = 0;
                _logger.LogInformation("Cache cleared successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
        }

        return Task.CompletedTask;
    }

    public int GetCacheCount()
    {
        return _currentItemCount;
    }

    private static string GetCacheKey<T>(string key)
    {
        return $"{typeof(T).Name}:{key}";
    }
}
