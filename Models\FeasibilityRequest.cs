using Newtonsoft.Json;

namespace OctopiFeasibilityChecker.Models;

public class FeasibilityRequest
{
    [JsonProperty("address")]
    public string Address { get; set; } = string.Empty;
}

public class FeasibilityResponse
{
    [JsonProperty("feasible")]
    public bool Feasible { get; set; }

    [JsonProperty("fno")]
    public string Fno { get; set; } = string.Empty;

    [JsonProperty("provider")]
    public string Provider { get; set; } = string.Empty;

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("error")]
    public string? Error { get; set; }
}

public class FeasibilityResult
{
    public string Address { get; set; } = string.Empty;
    public bool Feasible { get; set; }
    public string Fno { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string? Message { get; set; }
    public string? Error { get; set; }
    public DateTime CheckedAt { get; set; }
    public bool FromCache { get; set; }

    public string GetDisplayText()
    {
        if (!string.IsNullOrEmpty(Error))
        {
            return $"Error: {Error}";
        }

        if (Feasible)
        {
            return $"Fibre Available: Yes | FNO: {Fno} | Provider: {Provider}";
        }
        else
        {
            return $"Fibre Available: No{(string.IsNullOrEmpty(Message) ? "" : $" | {Message}")}";
        }
    }
}

public class GooglePlacesResponse
{
    [JsonProperty("predictions")]
    public List<GooglePlacesPrediction> Predictions { get; set; } = new();

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;
}

public class GooglePlacesPrediction
{
    [JsonProperty("description")]
    public string Description { get; set; } = string.Empty;

    [JsonProperty("place_id")]
    public string PlaceId { get; set; } = string.Empty;
}

public class AddressSuggestion
{
    public string Description { get; set; } = string.Empty;
    public string Id { get; set; } = string.Empty;
    public AutocompleteProvider Provider { get; set; }
    public string ProviderName => Provider.ToString();
    public object? OriginalData { get; set; }

    public static AddressSuggestion FromGoogle(GooglePlacesPrediction prediction)
    {
        return new AddressSuggestion
        {
            Description = prediction.Description,
            Id = prediction.PlaceId,
            Provider = AutocompleteProvider.Google,
            OriginalData = prediction
        };
    }

    public static AddressSuggestion FromRadar(RadarAddress address)
    {
        return new AddressSuggestion
        {
            Description = address.FormattedAddress,
            Id = address.PlaceId ?? Guid.NewGuid().ToString(),
            Provider = AutocompleteProvider.Radar,
            OriginalData = address
        };
    }
}

// Radar API Response Models
public class RadarAutocompleteResponse
{
    [JsonProperty("addresses")]
    public List<RadarAddress> Addresses { get; set; } = new();

    [JsonProperty("meta")]
    public RadarMeta? Meta { get; set; }
}

public class RadarAddress
{
    [JsonProperty("formattedAddress")]
    public string FormattedAddress { get; set; } = string.Empty;

    [JsonProperty("placeId")]
    public string? PlaceId { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; } = string.Empty;

    [JsonProperty("countryCode")]
    public string CountryCode { get; set; } = string.Empty;

    [JsonProperty("state")]
    public string State { get; set; } = string.Empty;

    [JsonProperty("city")]
    public string City { get; set; } = string.Empty;

    [JsonProperty("postalCode")]
    public string PostalCode { get; set; } = string.Empty;

    [JsonProperty("streetAddress")]
    public string StreetAddress { get; set; } = string.Empty;

    [JsonProperty("confidence")]
    public string Confidence { get; set; } = string.Empty;
}

public class RadarMeta
{
    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; } = string.Empty;
}
