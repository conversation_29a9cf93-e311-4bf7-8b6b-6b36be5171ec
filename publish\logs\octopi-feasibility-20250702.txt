2025-07-02 06:33:55.842 +02:00 [WRN] Google Places API not configured. Address autocomplete will not be available.
2025-07-02 06:34:00.446 +02:00 [INF] Saved 2 API providers to configuration
2025-07-02 06:41:54.138 +02:00 [WRN] Google Places API not configured. Address autocomplete will not be available.
2025-07-02 06:44:54.985 +02:00 [INF] Saved 2 API providers to configuration
2025-07-02 06:44:54.992 +02:00 [ERR] Error updating app settings file
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
2025-07-02 06:44:55.009 +02:00 [ERR] Error saving Google API settings
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
2025-07-02 06:44:55.009 +02:00 [ERR] Error saving configuration
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
   at OctopiFeasibilityChecker.ConfigurationForm.btnSave_Click(Object sender, EventArgs e) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\ConfigurationForm.cs:line 103
2025-07-02 06:45:15.231 +02:00 [INF] Saved 2 API providers to configuration
2025-07-02 06:45:15.232 +02:00 [ERR] Error updating app settings file
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
2025-07-02 06:45:15.232 +02:00 [ERR] Error saving Google API settings
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
2025-07-02 06:45:15.232 +02:00 [ERR] Error saving configuration
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
   at OctopiFeasibilityChecker.ConfigurationForm.btnSave_Click(Object sender, EventArgs e) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\ConfigurationForm.cs:line 103
2025-07-02 06:50:43.866 +02:00 [WRN] Google Places API not configured. Address autocomplete will not be available.
2025-07-02 06:50:49.857 +02:00 [INF] Saved 2 API providers to configuration
2025-07-02 06:50:49.865 +02:00 [ERR] Error updating app settings file
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
2025-07-02 06:50:49.882 +02:00 [ERR] Error saving Google API settings
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
2025-07-02 06:50:49.882 +02:00 [ERR] Error saving configuration
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
   at OctopiFeasibilityChecker.ConfigurationForm.btnSave_Click(Object sender, EventArgs e) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\ConfigurationForm.cs:line 103
2025-07-02 06:50:57.865 +02:00 [INF] Saved 2 API providers to configuration
2025-07-02 06:50:57.866 +02:00 [ERR] Error updating app settings file
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
2025-07-02 06:50:57.867 +02:00 [ERR] Error saving Google API settings
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
2025-07-02 06:50:57.867 +02:00 [ERR] Error saving configuration
System.InvalidCastException: Specified cast is not valid.
   at OctopiFeasibilityChecker.Services.ConfigurationService.UnflattenConfig(Dictionary`2 flatConfig) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 276
   at OctopiFeasibilityChecker.Services.ConfigurationService.UpdateAppSettingsAsync(Action`1 updateAction) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 224
   at OctopiFeasibilityChecker.Services.ConfigurationService.SaveGoogleApiSettingsAsync(GoogleApiSettings settings) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\ConfigurationService.cs:line 91
   at OctopiFeasibilityChecker.ConfigurationForm.btnSave_Click(Object sender, EventArgs e) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\ConfigurationForm.cs:line 103
2025-07-02 07:22:58.331 +02:00 [WRN] Google Places API not configured. Address autocomplete will not be available.
