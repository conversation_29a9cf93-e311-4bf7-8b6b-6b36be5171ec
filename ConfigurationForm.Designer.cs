namespace OctopiFeasibilityChecker
{
    partial class ConfigurationForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl = new TabControl();
            this.tabApiProviders = new TabPage();
            this.lstApiProviders = new ListView();
            this.colName = new ColumnHeader();
            this.colBaseUrl = new ColumnHeader();
            this.colEnabled = new ColumnHeader();
            this.colDefault = new ColumnHeader();
            this.colLastTested = new ColumnHeader();
            this.btnAddProvider = new Button();
            this.btnEditProvider = new Button();
            this.btnDeleteProvider = new Button();
            this.btnTestProvider = new Button();
            this.btnSetDefault = new Button();
            this.tabGoogleApi = new TabPage();
            this.lblGoogleApiKey = new Label();
            this.txtGoogleApiKey = new TextBox();
            this.chkGoogleApiEnabled = new CheckBox();
            this.lblGoogleBaseUrl = new Label();
            this.txtGoogleBaseUrl = new TextBox();
            this.lblGoogleEndpoint = new Label();
            this.txtGoogleEndpoint = new TextBox();
            this.lblCountryRestriction = new Label();
            this.txtCountryRestriction = new TextBox();
            this.lblPlaceTypes = new Label();
            this.txtPlaceTypes = new TextBox();
            this.tabAppSettings = new TabPage();
            this.chkUseDemo = new CheckBox();
            this.lblCacheExpiration = new Label();
            this.numCacheExpiration = new NumericUpDown();
            this.lblCacheMinutes = new Label();
            this.lblMaxCacheItems = new Label();
            this.numMaxCacheItems = new NumericUpDown();
            this.lblExportDirectory = new Label();
            this.txtExportDirectory = new TextBox();
            this.lblDateFormat = new Label();
            this.txtDateFormat = new TextBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.tabControl.SuspendLayout();
            this.tabApiProviders.SuspendLayout();
            this.tabGoogleApi.SuspendLayout();
            this.tabAppSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCacheExpiration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxCacheItems)).BeginInit();
            this.SuspendLayout();
            
            // 
            // tabControl
            // 
            this.tabControl.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.tabControl.Controls.Add(this.tabApiProviders);
            this.tabControl.Controls.Add(this.tabGoogleApi);
            this.tabControl.Controls.Add(this.tabAppSettings);
            this.tabControl.Location = new Point(12, 12);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new Size(760, 500);
            this.tabControl.TabIndex = 0;
            
            // 
            // tabApiProviders
            // 
            this.tabApiProviders.Controls.Add(this.lstApiProviders);
            this.tabApiProviders.Controls.Add(this.btnAddProvider);
            this.tabApiProviders.Controls.Add(this.btnEditProvider);
            this.tabApiProviders.Controls.Add(this.btnDeleteProvider);
            this.tabApiProviders.Controls.Add(this.btnTestProvider);
            this.tabApiProviders.Controls.Add(this.btnSetDefault);
            this.tabApiProviders.Location = new Point(4, 24);
            this.tabApiProviders.Name = "tabApiProviders";
            this.tabApiProviders.Padding = new Padding(3);
            this.tabApiProviders.Size = new Size(752, 472);
            this.tabApiProviders.TabIndex = 0;
            this.tabApiProviders.Text = "API Providers";
            this.tabApiProviders.UseVisualStyleBackColor = true;
            
            // 
            // lstApiProviders
            // 
            this.lstApiProviders.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.lstApiProviders.FullRowSelect = true;
            this.lstApiProviders.GridLines = true;
            this.lstApiProviders.Location = new Point(6, 6);
            this.lstApiProviders.MultiSelect = false;
            this.lstApiProviders.Name = "lstApiProviders";
            this.lstApiProviders.Size = new Size(740, 400);
            this.lstApiProviders.TabIndex = 0;
            this.lstApiProviders.UseCompatibleStateImageBehavior = false;
            this.lstApiProviders.View = View.Details;
            this.lstApiProviders.Columns.Add(this.colName);
            this.lstApiProviders.Columns.Add(this.colBaseUrl);
            this.lstApiProviders.Columns.Add(this.colEnabled);
            this.lstApiProviders.Columns.Add(this.colDefault);
            this.lstApiProviders.Columns.Add(this.colLastTested);
            
            // 
            // colName
            // 
            this.colName.Text = "Name";
            this.colName.Width = 200;
            
            // 
            // colBaseUrl
            // 
            this.colBaseUrl.Text = "Base URL";
            this.colBaseUrl.Width = 250;
            
            // 
            // colEnabled
            // 
            this.colEnabled.Text = "Enabled";
            this.colEnabled.Width = 80;
            
            // 
            // colDefault
            // 
            this.colDefault.Text = "Default";
            this.colDefault.Width = 80;
            
            // 
            // colLastTested
            // 
            this.colLastTested.Text = "Last Tested";
            this.colLastTested.Width = 120;
            
            // 
            // btnAddProvider
            // 
            this.btnAddProvider.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnAddProvider.Location = new Point(6, 415);
            this.btnAddProvider.Name = "btnAddProvider";
            this.btnAddProvider.Size = new Size(100, 30);
            this.btnAddProvider.TabIndex = 1;
            this.btnAddProvider.Text = "Add Provider";
            this.btnAddProvider.UseVisualStyleBackColor = true;
            this.btnAddProvider.Click += this.btnAddProvider_Click;
            
            // 
            // btnEditProvider
            // 
            this.btnEditProvider.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnEditProvider.Location = new Point(112, 415);
            this.btnEditProvider.Name = "btnEditProvider";
            this.btnEditProvider.Size = new Size(100, 30);
            this.btnEditProvider.TabIndex = 2;
            this.btnEditProvider.Text = "Edit Provider";
            this.btnEditProvider.UseVisualStyleBackColor = true;
            this.btnEditProvider.Click += this.btnEditProvider_Click;
            
            // 
            // btnDeleteProvider
            // 
            this.btnDeleteProvider.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnDeleteProvider.Location = new Point(218, 415);
            this.btnDeleteProvider.Name = "btnDeleteProvider";
            this.btnDeleteProvider.Size = new Size(100, 30);
            this.btnDeleteProvider.TabIndex = 3;
            this.btnDeleteProvider.Text = "Delete Provider";
            this.btnDeleteProvider.UseVisualStyleBackColor = true;
            this.btnDeleteProvider.Click += this.btnDeleteProvider_Click;
            
            // 
            // btnTestProvider
            // 
            this.btnTestProvider.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnTestProvider.Location = new Point(324, 415);
            this.btnTestProvider.Name = "btnTestProvider";
            this.btnTestProvider.Size = new Size(100, 30);
            this.btnTestProvider.TabIndex = 4;
            this.btnTestProvider.Text = "Test Provider";
            this.btnTestProvider.UseVisualStyleBackColor = true;
            this.btnTestProvider.Click += this.btnTestProvider_Click;
            
            // 
            // btnSetDefault
            // 
            this.btnSetDefault.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnSetDefault.Location = new Point(430, 415);
            this.btnSetDefault.Name = "btnSetDefault";
            this.btnSetDefault.Size = new Size(100, 30);
            this.btnSetDefault.TabIndex = 5;
            this.btnSetDefault.Text = "Set Default";
            this.btnSetDefault.UseVisualStyleBackColor = true;
            this.btnSetDefault.Click += this.btnSetDefault_Click;
            
            // 
            // tabGoogleApi
            // 
            this.tabGoogleApi.Controls.Add(this.lblGoogleApiKey);
            this.tabGoogleApi.Controls.Add(this.txtGoogleApiKey);
            this.tabGoogleApi.Controls.Add(this.chkGoogleApiEnabled);
            this.tabGoogleApi.Controls.Add(this.lblGoogleBaseUrl);
            this.tabGoogleApi.Controls.Add(this.txtGoogleBaseUrl);
            this.tabGoogleApi.Controls.Add(this.lblGoogleEndpoint);
            this.tabGoogleApi.Controls.Add(this.txtGoogleEndpoint);
            this.tabGoogleApi.Controls.Add(this.lblCountryRestriction);
            this.tabGoogleApi.Controls.Add(this.txtCountryRestriction);
            this.tabGoogleApi.Controls.Add(this.lblPlaceTypes);
            this.tabGoogleApi.Controls.Add(this.txtPlaceTypes);
            this.tabGoogleApi.Location = new Point(4, 24);
            this.tabGoogleApi.Name = "tabGoogleApi";
            this.tabGoogleApi.Padding = new Padding(3);
            this.tabGoogleApi.Size = new Size(752, 472);
            this.tabGoogleApi.TabIndex = 1;
            this.tabGoogleApi.Text = "Google Places API";
            this.tabGoogleApi.UseVisualStyleBackColor = true;
            
            // 
            // lblGoogleApiKey
            // 
            this.lblGoogleApiKey.AutoSize = true;
            this.lblGoogleApiKey.Location = new Point(20, 25);
            this.lblGoogleApiKey.Name = "lblGoogleApiKey";
            this.lblGoogleApiKey.Size = new Size(54, 15);
            this.lblGoogleApiKey.TabIndex = 0;
            this.lblGoogleApiKey.Text = "API Key:";
            
            // 
            // txtGoogleApiKey
            // 
            this.txtGoogleApiKey.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtGoogleApiKey.Location = new Point(150, 22);
            this.txtGoogleApiKey.Name = "txtGoogleApiKey";
            this.txtGoogleApiKey.Size = new Size(580, 23);
            this.txtGoogleApiKey.TabIndex = 1;
            this.txtGoogleApiKey.TextChanged += this.txtGoogleApiKey_TextChanged;
            
            // 
            // chkGoogleApiEnabled
            // 
            this.chkGoogleApiEnabled.AutoSize = true;
            this.chkGoogleApiEnabled.Location = new Point(150, 55);
            this.chkGoogleApiEnabled.Name = "chkGoogleApiEnabled";
            this.chkGoogleApiEnabled.Size = new Size(68, 19);
            this.chkGoogleApiEnabled.TabIndex = 2;
            this.chkGoogleApiEnabled.Text = "Enabled";
            this.chkGoogleApiEnabled.UseVisualStyleBackColor = true;
            this.chkGoogleApiEnabled.CheckedChanged += this.chkGoogleApiEnabled_CheckedChanged;
            
            // 
            // lblGoogleBaseUrl
            // 
            this.lblGoogleBaseUrl.AutoSize = true;
            this.lblGoogleBaseUrl.Location = new Point(20, 85);
            this.lblGoogleBaseUrl.Name = "lblGoogleBaseUrl";
            this.lblGoogleBaseUrl.Size = new Size(60, 15);
            this.lblGoogleBaseUrl.TabIndex = 3;
            this.lblGoogleBaseUrl.Text = "Base URL:";
            
            // 
            // txtGoogleBaseUrl
            // 
            this.txtGoogleBaseUrl.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtGoogleBaseUrl.Location = new Point(150, 82);
            this.txtGoogleBaseUrl.Name = "txtGoogleBaseUrl";
            this.txtGoogleBaseUrl.Size = new Size(580, 23);
            this.txtGoogleBaseUrl.TabIndex = 4;
            
            // 
            // lblGoogleEndpoint
            // 
            this.lblGoogleEndpoint.AutoSize = true;
            this.lblGoogleEndpoint.Location = new Point(20, 115);
            this.lblGoogleEndpoint.Name = "lblGoogleEndpoint";
            this.lblGoogleEndpoint.Size = new Size(124, 15);
            this.lblGoogleEndpoint.TabIndex = 5;
            this.lblGoogleEndpoint.Text = "Autocomplete Endpoint:";
            
            // 
            // txtGoogleEndpoint
            // 
            this.txtGoogleEndpoint.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtGoogleEndpoint.Location = new Point(150, 112);
            this.txtGoogleEndpoint.Name = "txtGoogleEndpoint";
            this.txtGoogleEndpoint.Size = new Size(580, 23);
            this.txtGoogleEndpoint.TabIndex = 6;
            
            // 
            // lblCountryRestriction
            // 
            this.lblCountryRestriction.AutoSize = true;
            this.lblCountryRestriction.Location = new Point(20, 145);
            this.lblCountryRestriction.Name = "lblCountryRestriction";
            this.lblCountryRestriction.Size = new Size(107, 15);
            this.lblCountryRestriction.TabIndex = 7;
            this.lblCountryRestriction.Text = "Country Restriction:";
            
            // 
            // txtCountryRestriction
            // 
            this.txtCountryRestriction.Location = new Point(150, 142);
            this.txtCountryRestriction.Name = "txtCountryRestriction";
            this.txtCountryRestriction.Size = new Size(100, 23);
            this.txtCountryRestriction.TabIndex = 8;
            
            // 
            // lblPlaceTypes
            // 
            this.lblPlaceTypes.AutoSize = true;
            this.lblPlaceTypes.Location = new Point(20, 175);
            this.lblPlaceTypes.Name = "lblPlaceTypes";
            this.lblPlaceTypes.Size = new Size(70, 15);
            this.lblPlaceTypes.TabIndex = 9;
            this.lblPlaceTypes.Text = "Place Types:";
            
            // 
            // txtPlaceTypes
            // 
            this.txtPlaceTypes.Location = new Point(150, 172);
            this.txtPlaceTypes.Name = "txtPlaceTypes";
            this.txtPlaceTypes.Size = new Size(200, 23);
            this.txtPlaceTypes.TabIndex = 10;
            
            // 
            // tabAppSettings
            // 
            this.tabAppSettings.Controls.Add(this.chkUseDemo);
            this.tabAppSettings.Controls.Add(this.lblCacheExpiration);
            this.tabAppSettings.Controls.Add(this.numCacheExpiration);
            this.tabAppSettings.Controls.Add(this.lblCacheMinutes);
            this.tabAppSettings.Controls.Add(this.lblMaxCacheItems);
            this.tabAppSettings.Controls.Add(this.numMaxCacheItems);
            this.tabAppSettings.Controls.Add(this.lblExportDirectory);
            this.tabAppSettings.Controls.Add(this.txtExportDirectory);
            this.tabAppSettings.Controls.Add(this.lblDateFormat);
            this.tabAppSettings.Controls.Add(this.txtDateFormat);
            this.tabAppSettings.Location = new Point(4, 24);
            this.tabAppSettings.Name = "tabAppSettings";
            this.tabAppSettings.Size = new Size(752, 472);
            this.tabAppSettings.TabIndex = 2;
            this.tabAppSettings.Text = "Application Settings";
            this.tabAppSettings.UseVisualStyleBackColor = true;
            
            // 
            // chkUseDemo
            // 
            this.chkUseDemo.AutoSize = true;
            this.chkUseDemo.Location = new Point(20, 25);
            this.chkUseDemo.Name = "chkUseDemo";
            this.chkUseDemo.Size = new Size(80, 19);
            this.chkUseDemo.TabIndex = 0;
            this.chkUseDemo.Text = "Use Demo";
            this.chkUseDemo.UseVisualStyleBackColor = true;
            
            // 
            // lblCacheExpiration
            // 
            this.lblCacheExpiration.AutoSize = true;
            this.lblCacheExpiration.Location = new Point(20, 65);
            this.lblCacheExpiration.Name = "lblCacheExpiration";
            this.lblCacheExpiration.Size = new Size(98, 15);
            this.lblCacheExpiration.TabIndex = 1;
            this.lblCacheExpiration.Text = "Cache Expiration:";
            
            // 
            // numCacheExpiration
            // 
            this.numCacheExpiration.Location = new Point(150, 63);
            this.numCacheExpiration.Maximum = new decimal(new int[] { 1440, 0, 0, 0 });
            this.numCacheExpiration.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            this.numCacheExpiration.Name = "numCacheExpiration";
            this.numCacheExpiration.Size = new Size(80, 23);
            this.numCacheExpiration.TabIndex = 2;
            this.numCacheExpiration.Value = new decimal(new int[] { 60, 0, 0, 0 });
            
            // 
            // lblCacheMinutes
            // 
            this.lblCacheMinutes.AutoSize = true;
            this.lblCacheMinutes.Location = new Point(240, 65);
            this.lblCacheMinutes.Name = "lblCacheMinutes";
            this.lblCacheMinutes.Size = new Size(50, 15);
            this.lblCacheMinutes.TabIndex = 3;
            this.lblCacheMinutes.Text = "minutes";
            
            // 
            // lblMaxCacheItems
            // 
            this.lblMaxCacheItems.AutoSize = true;
            this.lblMaxCacheItems.Location = new Point(20, 95);
            this.lblMaxCacheItems.Name = "lblMaxCacheItems";
            this.lblMaxCacheItems.Size = new Size(96, 15);
            this.lblMaxCacheItems.TabIndex = 4;
            this.lblMaxCacheItems.Text = "Max Cache Items:";
            
            // 
            // numMaxCacheItems
            // 
            this.numMaxCacheItems.Location = new Point(150, 93);
            this.numMaxCacheItems.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            this.numMaxCacheItems.Minimum = new decimal(new int[] { 10, 0, 0, 0 });
            this.numMaxCacheItems.Name = "numMaxCacheItems";
            this.numMaxCacheItems.Size = new Size(80, 23);
            this.numMaxCacheItems.TabIndex = 5;
            this.numMaxCacheItems.Value = new decimal(new int[] { 1000, 0, 0, 0 });
            
            // 
            // lblExportDirectory
            // 
            this.lblExportDirectory.AutoSize = true;
            this.lblExportDirectory.Location = new Point(20, 125);
            this.lblExportDirectory.Name = "lblExportDirectory";
            this.lblExportDirectory.Size = new Size(93, 15);
            this.lblExportDirectory.TabIndex = 6;
            this.lblExportDirectory.Text = "Export Directory:";
            
            // 
            // txtExportDirectory
            // 
            this.txtExportDirectory.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtExportDirectory.Location = new Point(150, 122);
            this.txtExportDirectory.Name = "txtExportDirectory";
            this.txtExportDirectory.Size = new Size(580, 23);
            this.txtExportDirectory.TabIndex = 7;
            
            // 
            // lblDateFormat
            // 
            this.lblDateFormat.AutoSize = true;
            this.lblDateFormat.Location = new Point(20, 155);
            this.lblDateFormat.Name = "lblDateFormat";
            this.lblDateFormat.Size = new Size(74, 15);
            this.lblDateFormat.TabIndex = 8;
            this.lblDateFormat.Text = "Date Format:";
            
            // 
            // txtDateFormat
            // 
            this.txtDateFormat.Location = new Point(150, 152);
            this.txtDateFormat.Name = "txtDateFormat";
            this.txtDateFormat.Size = new Size(200, 23);
            this.txtDateFormat.TabIndex = 9;
            
            // 
            // btnSave
            // 
            this.btnSave.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnSave.Location = new Point(616, 525);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(75, 30);
            this.btnSave.TabIndex = 1;
            this.btnSave.Text = "Save";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += this.btnSave_Click;
            
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnCancel.Location = new Point(697, 525);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(75, 30);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += this.btnCancel_Click;
            
            // 
            // ConfigurationForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(784, 567);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.tabControl);
            this.MinimumSize = new Size(800, 600);
            this.Name = "ConfigurationForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "Configuration - Octopi Feasibility Checker";
            this.Icon = LoadIcon();
            this.tabControl.ResumeLayout(false);
            this.tabApiProviders.ResumeLayout(false);
            this.tabGoogleApi.ResumeLayout(false);
            this.tabGoogleApi.PerformLayout();
            this.tabAppSettings.ResumeLayout(false);
            this.tabAppSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCacheExpiration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxCacheItems)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl;
        private TabPage tabApiProviders;
        private ListView lstApiProviders;
        private ColumnHeader colName;
        private ColumnHeader colBaseUrl;
        private ColumnHeader colEnabled;
        private ColumnHeader colDefault;
        private ColumnHeader colLastTested;
        private Button btnAddProvider;
        private Button btnEditProvider;
        private Button btnDeleteProvider;
        private Button btnTestProvider;
        private Button btnSetDefault;
        private TabPage tabGoogleApi;
        private Label lblGoogleApiKey;
        private TextBox txtGoogleApiKey;
        private CheckBox chkGoogleApiEnabled;
        private Label lblGoogleBaseUrl;
        private TextBox txtGoogleBaseUrl;
        private Label lblGoogleEndpoint;
        private TextBox txtGoogleEndpoint;
        private Label lblCountryRestriction;
        private TextBox txtCountryRestriction;
        private Label lblPlaceTypes;
        private TextBox txtPlaceTypes;
        private TabPage tabAppSettings;
        private CheckBox chkUseDemo;
        private Label lblCacheExpiration;
        private NumericUpDown numCacheExpiration;
        private Label lblCacheMinutes;
        private Label lblMaxCacheItems;
        private NumericUpDown numMaxCacheItems;
        private Label lblExportDirectory;
        private TextBox txtExportDirectory;
        private Label lblDateFormat;
        private TextBox txtDateFormat;
        private Button btnSave;
        private Button btnCancel;
    }
}
