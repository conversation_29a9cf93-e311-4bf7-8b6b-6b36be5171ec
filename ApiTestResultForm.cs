using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker;

public partial class ApiTestResultForm : Form
{
    private readonly ApiTestResult _testResult;

    public ApiTestResultForm(ApiTestResult testResult)
    {
        _testResult = testResult;
        InitializeComponent();
        LoadTestResult();
    }

    private void LoadTestResult()
    {
        // Set form title
        this.Text = _testResult.IsSuccessful ? "API Test - SUCCESS" : "API Test - FAILED";

        // Set status icon and color
        if (_testResult.IsSuccessful)
        {
            lblStatus.Text = "✅ SUCCESS";
            lblStatus.ForeColor = Color.Green;
            this.BackColor = Color.FromArgb(240, 255, 240); // Light green background
        }
        else
        {
            lblStatus.Text = "❌ FAILED";
            lblStatus.ForeColor = Color.Red;
            this.BackColor = Color.FromArgb(255, 240, 240); // Light red background
        }

        // Load basic information
        lblMessage.Text = _testResult.Message;
        lblResponseTime.Text = $"{_testResult.ResponseTimeMs} ms";
        lblStatusCode.Text = _testResult.StatusCode.ToString();
        lblTestedAt.Text = _testResult.TestedAt.ToString("yyyy-MM-dd HH:mm:ss");

        // Load detailed information
        txtDetails.Text = _testResult.GetDetailedReport();

        // Show/hide error details
        if (!string.IsNullOrEmpty(_testResult.ErrorDetails))
        {
            txtErrorDetails.Text = _testResult.ErrorDetails;
            txtErrorDetails.Visible = true;
            lblErrorDetails.Visible = true;
        }
        else
        {
            txtErrorDetails.Visible = false;
            lblErrorDetails.Visible = false;
        }

        // Show/hide response content
        if (!string.IsNullOrEmpty(_testResult.ResponseContent))
        {
            txtResponseContent.Text = _testResult.ResponseContent;
            
            // Try to format JSON
            try
            {
                var formatted = Newtonsoft.Json.JsonConvert.DeserializeObject(_testResult.ResponseContent);
                txtResponseContent.Text = Newtonsoft.Json.JsonConvert.SerializeObject(formatted, Newtonsoft.Json.Formatting.Indented);
            }
            catch
            {
                // Keep original content if not valid JSON
            }
        }
        else
        {
            txtResponseContent.Text = "No response content received";
        }
    }

    private void btnClose_Click(object sender, EventArgs e)
    {
        this.Close();
    }

    private void btnCopyReport_Click(object sender, EventArgs e)
    {
        try
        {
            Clipboard.SetText(_testResult.GetDetailedReport());
            MessageBox.Show("Test report copied to clipboard!", "Copied", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error copying to clipboard: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void btnRetryTest_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Retry;
        this.Close();
    }
}
