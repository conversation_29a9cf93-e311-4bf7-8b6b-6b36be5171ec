# How to Run Octopi Feasibility Checker

## 🎯 **IMPORTANT: Demo Mode Active**

**The application now runs in DEMO MODE** because the real Octopi portal doesn't provide a public API. See `API-DISCOVERY.md` for full details.

## 🚀 **EASIEST WAY - Standalone Executable**

### Step 1: Build the Executable
```
Double-click: build-exe.bat
```
This creates a single `.exe` file that doesn't need .NET installed.

### Step 2: Run the Application
```
Double-click: run-app.bat
OR
Double-click: publish\OctopiFeasibilityChecker.exe
```

**That's it!** The application will start and you can begin checking fibre feasibility.

---

## 📁 **What Gets Created**

After running `build-exe.bat`, you'll have:

```
publish/
├── OctopiFeasibilityChecker.exe  ← Main executable (run this!)
├── appsettings.json              ← Configuration file
└── OctopiFeasibilityChecker.pdb  ← Debug symbols (optional)
```

## 🎯 **Quick Test (Demo Mode)**

1. Start the application (you'll see a demo mode notification)
2. Click OK to continue
3. Enter test address: `6 Russell St, Mbombela, 1201`
4. Click "Check FNO"
5. You'll get realistic simulated feasibility information
6. Try different addresses to see various responses

## 📦 **Portable Installation**

The `publish` folder is completely portable:
- Copy the entire `publish` folder to any Windows computer
- Run `OctopiFeasibilityChecker.exe` directly
- No installation or .NET required on the target machine

## 🔧 **Available Scripts**

| Script | Purpose |
|--------|---------|
| `build-exe.bat` | Creates standalone executable |
| `run-app.bat` | Runs the application (builds if needed) |
| `build-and-run.bat` | Builds and immediately runs |

## ⚙️ **Configuration (Optional)**

To enable Google Places autocomplete:
1. Edit `publish\appsettings.json`
2. Replace `YOUR_GOOGLE_PLACES_API_KEY_HERE` with your API key
3. Restart the application

## 🆘 **Troubleshooting**

### "Build failed"
- Make sure you have .NET 6 SDK installed
- Run `dotnet --version` to check

### "Application won't start"
- Try running from command line to see error messages
- Check if Windows Defender is blocking the executable
- Ensure you're on Windows 10/11 64-bit

### "API errors"
- Check your internet connection
- Verify Octopi API is accessible
- Check logs in the application folder

## 📋 **File Sizes**

The standalone executable is approximately:
- **OctopiFeasibilityChecker.exe**: ~60-80 MB
- **Total publish folder**: ~80-100 MB

This is normal for self-contained .NET applications as they include the entire runtime.

---

## 🎉 **You're Ready!**

The application includes all requested features:
- ✅ Single address checking
- ✅ Batch processing from files
- ✅ CSV export
- ✅ Address autocomplete (with API key)
- ✅ Caching and retry logic
- ✅ Comprehensive error handling

Just run `build-exe.bat` and then `run-app.bat` to get started!
