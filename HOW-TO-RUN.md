# How to Run Octopi Feasibility Checker

## 🎯 **NEW FEATURES ADDED**

✅ **Menu System with Configuration**
- File → Configuration: Manage API providers and settings
- File → Exit: Close application
- Help → About: Application information

✅ **API Provider Management**
- Add multiple API providers (28East, custom APIs)
- Configure endpoints, authentication, timeouts
- Test API connections
- Set default providers

✅ **Google Places API Configuration**
- Easy setup through configuration menu
- Enable/disable address autocomplete
- Country restrictions and place types

✅ **Application Icon**
- Uses your OCTOPI.png file as the application icon

✅ **API Testing Feature**
- **Test Provider button**: Real API endpoint testing
- **Detailed test results**: Success/failure with response times
- **Error diagnostics**: Network, timeout, and format validation
- **Response analysis**: JSON structure and field validation

✅ **Bug Fixes Applied**
- Fixed "Error when adding provider" issue
- Fixed "Specified cast not available" when disabling demo mode
- Improved JSON handling and configuration parsing
- Added robust error handling throughout

## 🎯 **IMPORTANT: Demo Mode Active**

**The application runs in DEMO MODE by default** because the real Octopi portal doesn't provide a public API. See `API-DISCOVERY.md` for full details.

## 🚀 **EASIEST WAY - Standalone Executable**

### Step 1: Build the Executable
```
Double-click: build-exe.bat
```
This creates a single `.exe` file that doesn't need .NET installed.

### Step 2: Run the Application
```
Double-click: run-app.bat
OR
Double-click: publish\OctopiFeasibilityChecker.exe
```

**That's it!** The application will start and you can begin checking fibre feasibility.

---

## 📁 **What Gets Created**

After running `build-exe.bat`, you'll have:

```
publish/
├── OctopiFeasibilityChecker.exe  ← Main executable (run this!)
├── appsettings.json              ← Configuration file
└── OctopiFeasibilityChecker.pdb  ← Debug symbols (optional)
```

## 🎯 **Quick Test (Demo Mode)**

1. Start the application (you'll see a demo mode notification)
2. Click OK to continue
3. **Try the Configuration Menu**: File → Configuration
   - View API Providers tab
   - Set up Google Places API (optional)
   - Adjust application settings
4. Enter test address: `6 Russell St, Mbombela, 1201`
5. Click "Check FNO"
6. You'll get realistic simulated feasibility information
7. Try different addresses to see various responses

## ⚙️ **Configuration Guide**

### **API Providers Tab**
- **Add Provider**: Click "Add Provider" to create new API configurations
- **Edit Provider**: Select and edit existing providers
- **Set Default**: Choose which provider to use by default
- **Test Provider**: 🧪 **NEW!** Test API connectivity with detailed results
  - Shows success/failure status
  - Measures response time
  - Validates JSON structure
  - Provides detailed error diagnostics

### **Google Places API Tab**
1. Get API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Places API
3. Enter your API key in the configuration
4. Check "Enabled" to activate autocomplete
5. Adjust country restrictions (default: South Africa)

### **Application Settings Tab**
- **Use Demo**: Toggle between demo and real API mode
- **Cache Settings**: Configure cache expiration and limits
- **Export Settings**: Set default export directory and date format

## 📦 **Portable Installation**

The `publish` folder is completely portable:
- Copy the entire `publish` folder to any Windows computer
- Run `OctopiFeasibilityChecker.exe` directly
- No installation or .NET required on the target machine

## 🔧 **Available Scripts**

| Script | Purpose |
|--------|---------|
| `build-exe.bat` | Creates standalone executable |
| `run-app.bat` | Runs the application (builds if needed) |
| `build-and-run.bat` | Builds and immediately runs |

## ⚙️ **Configuration (Optional)**

To enable Google Places autocomplete:
1. Edit `publish\appsettings.json`
2. Replace `YOUR_GOOGLE_PLACES_API_KEY_HERE` with your API key
3. Restart the application

## 🆘 **Troubleshooting**

### "Build failed"
- Make sure you have .NET 6 SDK installed
- Run `dotnet --version` to check

### "Application won't start"
- Try running from command line to see error messages
- Check if Windows Defender is blocking the executable
- Ensure you're on Windows 10/11 64-bit

### "API errors"
- Check your internet connection
- Verify Octopi API is accessible
- Check logs in the application folder

## 📋 **File Sizes**

The standalone executable is approximately:
- **OctopiFeasibilityChecker.exe**: ~60-80 MB
- **Total publish folder**: ~80-100 MB

This is normal for self-contained .NET applications as they include the entire runtime.

---

## 🎉 **You're Ready!**

The application includes all requested features:
- ✅ Single address checking
- ✅ Batch processing from files
- ✅ CSV export
- ✅ Address autocomplete (with API key)
- ✅ Caching and retry logic
- ✅ Comprehensive error handling

Just run `build-exe.bat` and then `run-app.bat` to get started!
