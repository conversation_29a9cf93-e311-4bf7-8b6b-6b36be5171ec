# Troubleshooting Guide

## 🔧 **Fixed Issues**

### ✅ **"Error when adding provider" - FIXED**
**Problem**: JSON deserialization errors when saving configuration
**Solution**: 
- Added robust JSON handling with try-catch blocks
- Improved object type detection for nested JSON structures
- Added fallback to empty configuration if JSON is invalid

### ✅ **"Specified cast not available" when removing demo - FIXED**
**Problem**: Boolean parsing failed when configuration values weren't valid booleans
**Solution**:
- Replaced `bool.Parse()` with safe `bool.TryParse()` methods
- Added helper methods `ParseBool()` and `ParseInt()` with default values
- Graceful fallback to default values if parsing fails

## 🚀 **How to Use Configuration**

### **Adding API Providers**
1. **Open Configuration**: File → Configuration
2. **Go to API Providers tab**
3. **Click "Add Provider"**
4. **Fill in Basic Settings**:
   - Name: e.g., "28East Octopi"
   - Base URL: e.g., "https://octopi.28east.co.za"
   - Feasibility Endpoint: e.g., "/api/feasibility"
   - API Key: (if required)
5. **Configure Advanced Settings** (optional):
   - Timeout, retry attempts, request method
   - Custom request body template
6. **Set Response Mapping** (optional):
   - Field names for feasible, fno, provider, etc.
7. **Click Save**

### **Switching from Demo Mode**
1. **Open Configuration**: File → Configuration
2. **Go to Application Settings tab**
3. **Uncheck "Use Demo"**
4. **Click Save**
5. **Restart application when prompted**

### **Setting Up Google Places API**
1. **Get API Key**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create project and enable Places API
   - Create API key
2. **Configure in App**:
   - File → Configuration → Google Places API tab
   - Enter API key
   - Check "Enabled"
   - Adjust settings as needed
3. **Save and restart**

## 🐛 **Common Issues & Solutions**

### **Configuration Won't Save**
- **Check file permissions** in application directory
- **Run as administrator** if needed
- **Check disk space** availability

### **API Provider Test Fails**
- **Verify URL format** (must start with http:// or https://)
- **Check network connectivity**
- **Verify API endpoint exists**
- **Check authentication requirements**

### **Google Places Not Working**
- **Verify API key is correct**
- **Check billing is enabled** in Google Cloud
- **Ensure Places API is enabled**
- **Check API key restrictions**

### **Demo Mode Won't Disable**
- **Save configuration first**
- **Restart application** after changing demo mode
- **Check that at least one API provider is enabled**

### **Application Won't Start**
- **Check .NET 6 is installed**
- **Verify all files are in publish folder**
- **Check Windows Defender isn't blocking**
- **Run from command line to see error messages**

## 📁 **Configuration Files**

The application creates these configuration files:

- **`appsettings.json`** - Main application settings
- **`api-providers.json`** - API provider configurations
- **`logs/`** - Application logs (check for detailed errors)

### **Manual Configuration Reset**
If configuration gets corrupted:

1. **Close application**
2. **Delete these files**:
   - `appsettings.json`
   - `api-providers.json`
3. **Restart application** (will recreate with defaults)

## 🔍 **Debugging Tips**

### **Check Logs**
- Look in `logs/octopi-feasibility-YYYY-MM-DD.txt`
- Contains detailed error information
- Shows API request/response details

### **Test API Manually**
- Use the included `test-api.ps1` script
- Test endpoints with PowerShell or curl
- Verify JSON response format

### **Configuration Validation**
- Check JSON syntax in configuration files
- Verify URL formats are correct
- Ensure required fields are not empty

## 📞 **Getting Help**

If you encounter issues:

1. **Check the logs** in the `logs/` folder
2. **Try manual configuration reset**
3. **Test API endpoints manually**
4. **Check network connectivity**
5. **Verify all dependencies are installed**

The application now has robust error handling and should provide clear error messages for most issues!
