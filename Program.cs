using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OctopiFeasibilityChecker.Services;
using Serilog;

namespace OctopiFeasibilityChecker;

internal static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.File("logs/octopi-feasibility-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Setup dependency injection
            var services = new ServiceCollection();
            ConfigureServices(services, configuration);

            var serviceProvider = services.BuildServiceProvider();

            // Run the application
            var mainForm = serviceProvider.GetRequiredService<MainForm>();
            Application.Run(mainForm);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Add configuration
        services.AddSingleton(configuration);

        // Add logging
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });

        // Add HTTP client
        services.AddHttpClient<IOctopiService, OctopiService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        // Add memory cache
        services.AddMemoryCache();

        // Add services - choose between demo and real implementation
        var useDemoValue = configuration["OctopiApi:UseDemo"] ?? "true";
        var useDemo = bool.TryParse(useDemoValue, out bool result) ? result : true;
        if (useDemo)
        {
            services.AddSingleton<IOctopiService, DemoOctopiService>();
        }
        else
        {
            services.AddSingleton<IOctopiService, OctopiService>();
        }
        services.AddSingleton<ICacheService, CacheService>();
        services.AddSingleton<ICsvExportService, CsvExportService>();
        services.AddSingleton<IAddressAutocompleteService, AddressAutocompleteService>();
        services.AddSingleton<IConfigurationService, ConfigurationService>();

        // Add forms
        services.AddTransient<MainForm>();
        services.AddTransient<ConfigurationForm>();
    }
}
