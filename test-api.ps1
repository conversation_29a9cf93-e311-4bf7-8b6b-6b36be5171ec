# PowerShell script to test the Octopi API endpoint
Write-Host "=== Octopi API Endpoint Test ===" -ForegroundColor Green
Write-Host ""

$baseUrl = "https://octopi.28east.co.za"
$endpoint = "/api/feasibility"
$fullUrl = "$baseUrl$endpoint"
$testAddress = "6 Russell St, Mbombela, 1201"

Write-Host "Testing URL: $fullUrl" -ForegroundColor Yellow
Write-Host "Test Address: $testAddress" -ForegroundColor Yellow
Write-Host ""

# Test 1: Check if base URL is accessible
Write-Host "=== Test 1: Checking base URL accessibility ===" -ForegroundColor Cyan
try {
    $baseResponse = Invoke-WebRequest -Uri $baseUrl -Method GET -TimeoutSec 30
    Write-Host "Base URL Status: $($baseResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Base URL Content Length: $($baseResponse.Content.Length)"
    $preview = $baseResponse.Content.Substring(0, [Math]::Min(200, $baseResponse.Content.Length))
    Write-Host "Base URL Content Preview: $preview..."
}
catch {
    Write-Host "Base URL Error: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 2: Try GET request to feasibility endpoint
Write-Host "=== Test 2: GET request to feasibility endpoint ===" -ForegroundColor Cyan
try {
    $getResponse = Invoke-WebRequest -Uri $fullUrl -Method GET -TimeoutSec 30
    Write-Host "GET Status: $($getResponse.StatusCode)" -ForegroundColor Green
    Write-Host "GET Content Length: $($getResponse.Content.Length)"
    $preview = $getResponse.Content.Substring(0, [Math]::Min(500, $getResponse.Content.Length))
    Write-Host "GET Content Preview: $preview..."
}
catch {
    Write-Host "GET Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "GET Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

# Test 3: Try POST request with JSON body
Write-Host "=== Test 3: POST request with JSON body ===" -ForegroundColor Cyan
try {
    $requestBody = @{
        address = $testAddress
    } | ConvertTo-Json
    
    Write-Host "Request Body: $requestBody"
    Write-Host "Content-Type: application/json"
    
    $postResponse = Invoke-WebRequest -Uri $fullUrl -Method POST -Body $requestBody -ContentType "application/json" -TimeoutSec 30
    Write-Host "POST Status: $($postResponse.StatusCode)" -ForegroundColor Green
    Write-Host "POST Content Length: $($postResponse.Content.Length)"
    Write-Host "POST Content: $($postResponse.Content)" -ForegroundColor Green
}
catch {
    Write-Host "POST Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "POST Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        try {
            $errorContent = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorContent)
            $errorText = $reader.ReadToEnd()
            Write-Host "Error Response Content: $errorText" -ForegroundColor Red
        }
        catch {
            Write-Host "Could not read error response content" -ForegroundColor Red
        }
    }
}
Write-Host ""

# Test 4: Try POST with form data
Write-Host "=== Test 4: POST request with form data ===" -ForegroundColor Cyan
try {
    $formData = @{
        address = $testAddress
    }
    
    $formResponse = Invoke-WebRequest -Uri $fullUrl -Method POST -Body $formData -TimeoutSec 30
    Write-Host "Form POST Status: $($formResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Form POST Content: $($formResponse.Content)" -ForegroundColor Green
}
catch {
    Write-Host "Form POST Error: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 5: Try different endpoint variations
Write-Host "=== Test 5: Testing endpoint variations ===" -ForegroundColor Cyan
$endpointVariations = @(
    "/api/feasibility",
    "/feasibility", 
    "/api/check-feasibility",
    "/check-feasibility",
    "/api/fibre-check",
    "/fibre-check"
)

foreach ($endpointVar in $endpointVariations) {
    $testUrl = "$baseUrl$endpointVar"
    Write-Host "Testing: $testUrl" -ForegroundColor Yellow
    
    try {
        $requestBody = @{
            address = $testAddress
        } | ConvertTo-Json
        
        $response = Invoke-WebRequest -Uri $testUrl -Method POST -Body $requestBody -ContentType "application/json" -TimeoutSec 10
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Green
        if ($response.StatusCode -eq 200) {
            Write-Host "  Content: $($response.Content)" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
