[{"Id": "f8fc16df-49ed-4db8-afe0-56eff7383dea", "Name": "28East Octopi Portal", "BaseUrl": "https://octopi.28east.co.za", "FeasibilityEndpoint": "/api/feasibility", "ApiKey": null, "AuthHeader": null, "TimeoutSeconds": 30, "MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "IsEnabled": true, "IsDefault": true, "CreatedAt": "2025-07-02T06:29:11.0389555+02:00", "LastTestedAt": null, "LastTestSuccessful": null, "LastTestError": null, "RequestMethod": "POST", "ContentType": "application/json", "RequestBodyTemplate": "{\"address\":\"{ADDRESS}\"}", "FeasibleField": "feasible", "FnoField": "fno", "ProviderField": "provider", "MessageField": "message", "ErrorField": "error"}, {"Id": "02e38620-cf4c-4438-b877-da249913de0b", "Name": "Demo Provider", "BaseUrl": "demo://localhost", "FeasibilityEndpoint": "/demo", "ApiKey": null, "AuthHeader": null, "TimeoutSeconds": 30, "MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "IsEnabled": false, "IsDefault": false, "CreatedAt": "2025-07-02T06:29:11.0389871+02:00", "LastTestedAt": null, "LastTestSuccessful": null, "LastTestError": null, "RequestMethod": "POST", "ContentType": "application/json", "RequestBodyTemplate": "{\"address\":\"{ADDRESS}\"}", "FeasibleField": "feasible", "FnoField": "fno", "ProviderField": "provider", "MessageField": "message", "ErrorField": "error"}]