using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OctopiFeasibilityChecker.Models;
using System.Text;

namespace OctopiFeasibilityChecker.Services;

public class OctopiService : IOctopiService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<OctopiService> _logger;
    private readonly ICacheService _cacheService;
    private readonly string _baseUrl;
    private readonly string _feasibilityEndpoint;
    private readonly int _maxRetryAttempts;
    private readonly int _retryDelaySeconds;

    public OctopiService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<OctopiService> logger,
        ICacheService cacheService)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _cacheService = cacheService;

        _baseUrl = _configuration["OctopiApi:BaseUrl"] ?? "https://octopi.28east.co.za";
        _feasibilityEndpoint = _configuration["OctopiApi:FeasibilityEndpoint"] ?? "/api/feasibility";
        _maxRetryAttempts = int.Parse(_configuration["OctopiApi:MaxRetryAttempts"] ?? "3");
        _retryDelaySeconds = int.Parse(_configuration["OctopiApi:RetryDelaySeconds"] ?? "2");
    }

    public async Task<FeasibilityResult> CheckFeasibilityAsync(string address, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(address))
        {
            return new FeasibilityResult
            {
                Address = address,
                Error = "Address cannot be empty",
                CheckedAt = DateTime.Now
            };
        }

        // Check cache first
        var cachedResult = await _cacheService.GetAsync<FeasibilityResult>(address);
        if (cachedResult != null)
        {
            _logger.LogInformation("Retrieved feasibility result from cache for address: {Address}", address);
            cachedResult.FromCache = true;
            return cachedResult;
        }

        var result = await CheckFeasibilityWithRetryAsync(address, cancellationToken);
        
        // Cache successful results
        if (string.IsNullOrEmpty(result.Error))
        {
            await _cacheService.SetAsync(address, result);
        }

        return result;
    }

    public async Task<List<FeasibilityResult>> CheckMultipleFeasibilityAsync(
        IEnumerable<string> addresses, 
        IProgress<int>? progress = null, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<FeasibilityResult>();
        var addressList = addresses.ToList();
        var completed = 0;

        foreach (var address in addressList)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            try
            {
                var result = await CheckFeasibilityAsync(address, cancellationToken);
                results.Add(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feasibility for address: {Address}", address);
                results.Add(new FeasibilityResult
                {
                    Address = address,
                    Error = ex.Message,
                    CheckedAt = DateTime.Now
                });
            }

            completed++;
            progress?.Report(completed);

            // Small delay between requests to be respectful to the API
            if (completed < addressList.Count)
            {
                await Task.Delay(500, cancellationToken);
            }
        }

        return results;
    }

    private async Task<FeasibilityResult> CheckFeasibilityWithRetryAsync(string address, CancellationToken cancellationToken)
    {
        Exception? lastException = null;

        for (int attempt = 1; attempt <= _maxRetryAttempts; attempt++)
        {
            try
            {
                _logger.LogInformation("Checking feasibility for address: {Address} (Attempt {Attempt}/{MaxAttempts})", 
                    address, attempt, _maxRetryAttempts);

                var request = new FeasibilityRequest { Address = address };
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_baseUrl}{_feasibilityEndpoint}";
                var response = await _httpClient.PostAsync(url, content, cancellationToken);

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var feasibilityResponse = JsonConvert.DeserializeObject<FeasibilityResponse>(responseContent);
                    
                    if (feasibilityResponse != null)
                    {
                        return new FeasibilityResult
                        {
                            Address = address,
                            Feasible = feasibilityResponse.Feasible,
                            Fno = feasibilityResponse.Fno ?? string.Empty,
                            Provider = feasibilityResponse.Provider ?? string.Empty,
                            Message = feasibilityResponse.Message,
                            Error = feasibilityResponse.Error,
                            CheckedAt = DateTime.Now
                        };
                    }
                }
                else
                {
                    _logger.LogWarning("API returned error status {StatusCode}: {Content}", response.StatusCode, responseContent);
                    
                    if (attempt == _maxRetryAttempts)
                    {
                        return new FeasibilityResult
                        {
                            Address = address,
                            Error = $"API returned {response.StatusCode}: {responseContent}",
                            CheckedAt = DateTime.Now
                        };
                    }
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                lastException = ex;
                _logger.LogWarning("Request timeout for address: {Address} (Attempt {Attempt}/{MaxAttempts})", 
                    address, attempt, _maxRetryAttempts);
            }
            catch (Exception ex)
            {
                lastException = ex;
                _logger.LogError(ex, "Error checking feasibility for address: {Address} (Attempt {Attempt}/{MaxAttempts})", 
                    address, attempt, _maxRetryAttempts);
            }

            if (attempt < _maxRetryAttempts)
            {
                var delay = TimeSpan.FromSeconds(_retryDelaySeconds * attempt);
                _logger.LogInformation("Retrying in {Delay} seconds...", delay.TotalSeconds);
                await Task.Delay(delay, cancellationToken);
            }
        }

        return new FeasibilityResult
        {
            Address = address,
            Error = lastException?.Message ?? "Unknown error occurred",
            CheckedAt = DateTime.Now
        };
    }
}
