C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.csproj.AssemblyReference.cache
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.GeneratedMSBuildEditorConfig.editorconfig
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.AssemblyInfoInputs.cache
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.AssemblyInfo.cs
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.csproj.CoreCompileInputs.cache
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\appsettings.json
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\OctopiFeasibilityChecker.exe
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\OctopiFeasibilityChecker.deps.json
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\OctopiFeasibilityChecker.runtimeconfig.json
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\OctopiFeasibilityChecker.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\OctopiFeasibilityChecker.pdb
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\CsvHelper.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Memory.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Http.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Serilog.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Serilog.Extensions.Logging.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\bin\Debug\net6.0-windows\Serilog.Sinks.File.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.csproj.CopyComplete
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\refint\OctopiFeasibilityChecker.dll
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.pdb
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\OctopiFeasibilityChecker.genruntimeconfig.cache
C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\obj\Debug\net6.0-windows\ref\OctopiFeasibilityChecker.dll
