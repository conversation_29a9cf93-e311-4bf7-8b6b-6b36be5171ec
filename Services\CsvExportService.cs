using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OctopiFeasibilityChecker.Models;
using System.Globalization;
using System.Text;

namespace OctopiFeasibilityChecker.Services;

public class CsvExportService : ICsvExportService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<CsvExportService> _logger;
    private readonly string _defaultDirectory;
    private readonly string _dateFormat;

    public CsvExportService(IConfiguration configuration, ILogger<CsvExportService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _defaultDirectory = _configuration["Export:DefaultDirectory"] ?? "exports";
        _dateFormat = _configuration["Export:DateFormat"] ?? "yyyy-MM-dd_HH-mm-ss";

        // Ensure export directory exists
        if (!Directory.Exists(_defaultDirectory))
        {
            Directory.CreateDirectory(_defaultDirectory);
        }
    }

    public async Task<string> ExportResultsAsync(IEnumerable<FeasibilityResult> results, string? fileName = null)
    {
        try
        {
            var filePath = GetDefaultExportPath(fileName);
            
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                Encoding = Encoding.UTF8
            };

            using var writer = new StringWriter();
            using var csv = new CsvWriter(writer, config);

            // Write headers
            csv.WriteField("Address");
            csv.WriteField("Feasible");
            csv.WriteField("FNO");
            csv.WriteField("Provider");
            csv.WriteField("Message");
            csv.WriteField("Error");
            csv.WriteField("CheckedAt");
            csv.WriteField("FromCache");
            csv.NextRecord();

            // Write data
            foreach (var result in results)
            {
                csv.WriteField(result.Address);
                csv.WriteField(result.Feasible);
                csv.WriteField(result.Fno);
                csv.WriteField(result.Provider);
                csv.WriteField(result.Message ?? "");
                csv.WriteField(result.Error ?? "");
                csv.WriteField(result.CheckedAt.ToString("yyyy-MM-dd HH:mm:ss"));
                csv.WriteField(result.FromCache);
                csv.NextRecord();
            }

            var csvContent = writer.ToString();
            await File.WriteAllTextAsync(filePath, csvContent, Encoding.UTF8);

            _logger.LogInformation("Exported {Count} results to {FilePath}", results.Count(), filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting results to CSV");
            throw;
        }
    }

    public async Task<List<string>> ImportAddressesAsync(string filePath)
    {
        try
        {
            var addresses = new List<string>();

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var extension = Path.GetExtension(filePath).ToLowerInvariant();

            if (extension == ".csv")
            {
                addresses = await ImportFromCsvAsync(filePath);
            }
            else if (extension == ".txt")
            {
                addresses = await ImportFromTextAsync(filePath);
            }
            else
            {
                throw new NotSupportedException($"File type {extension} is not supported. Only .csv and .txt files are supported.");
            }

            // Remove empty lines and trim whitespace
            addresses = addresses
                .Where(a => !string.IsNullOrWhiteSpace(a))
                .Select(a => a.Trim())
                .Distinct()
                .ToList();

            _logger.LogInformation("Imported {Count} addresses from {FilePath}", addresses.Count, filePath);
            return addresses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing addresses from {FilePath}", filePath);
            throw;
        }
    }

    public string GetDefaultExportPath(string? fileName = null)
    {
        if (string.IsNullOrWhiteSpace(fileName))
        {
            var timestamp = DateTime.Now.ToString(_dateFormat);
            fileName = $"feasibility-results_{timestamp}.csv";
        }

        return Path.Combine(_defaultDirectory, fileName);
    }

    private async Task<List<string>> ImportFromCsvAsync(string filePath)
    {
        var addresses = new List<string>();
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true,
            MissingFieldFound = null,
            HeaderValidated = null
        };

        using var reader = new StreamReader(filePath, Encoding.UTF8);
        using var csv = new CsvReader(reader, config);

        await csv.ReadAsync();
        csv.ReadHeader();

        // Try to find address column (case-insensitive)
        var addressColumnIndex = -1;
        var possibleAddressHeaders = new[] { "address", "addresses", "location", "locations", "street", "addr" };
        
        for (int i = 0; i < csv.HeaderRecord?.Length; i++)
        {
            var header = csv.HeaderRecord[i]?.ToLowerInvariant() ?? "";
            if (possibleAddressHeaders.Contains(header))
            {
                addressColumnIndex = i;
                break;
            }
        }

        // If no address column found, assume first column contains addresses
        if (addressColumnIndex == -1)
        {
            addressColumnIndex = 0;
        }

        while (await csv.ReadAsync())
        {
            if (csv.TryGetField(addressColumnIndex, out string? address) && !string.IsNullOrWhiteSpace(address))
            {
                addresses.Add(address);
            }
        }

        return addresses;
    }

    private async Task<List<string>> ImportFromTextAsync(string filePath)
    {
        var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
        var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        return lines.ToList();
    }
}
