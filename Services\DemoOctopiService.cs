using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker.Services;

/// <summary>
/// Demo implementation of IOctopiService that simulates realistic fibre feasibility responses
/// This is used because the real Octopi portal doesn't provide a public API
/// </summary>
public class DemoOctopiService : IOctopiService
{
    private readonly ILogger<DemoOctopiService> _logger;
    private readonly ICacheService _cacheService;
    private readonly Random _random = new();

    // Realistic South African FNO providers
    private readonly string[] _fnoProviders = {
        "Openserve", "Vumatel", "Frogfoot", "MetroFibre", "Octotel", 
        "Evotel", "Herotel", "Lightstruck", "MFN", "Cybersmart"
    };

    // Common South African areas and their typical feasibility
    private readonly Dictionary<string, bool> _areaFeasibility = new()
    {
        // High feasibility areas
        { "sandton", true }, { "rosebank", true }, { "camps bay", true },
        { "stellenbosch", true }, { "centurion", true }, { "umhlanga", true },
        { "melville", true }, { "parkhurst", true }, { "claremont", true },
        
        // Medium feasibility areas  
        { "johannesburg", true }, { "cape town", true }, { "durban", true },
        { "pretoria", true }, { "port elizabeth", true }, { "bloemfontein", false },
        
        // Lower feasibility areas
        { "rural", false }, { "township", false }, { "farm", false }
    };

    public DemoOctopiService(
        ILogger<DemoOctopiService> logger,
        ICacheService cacheService)
    {
        _logger = logger;
        _cacheService = cacheService;
    }

    public async Task<FeasibilityResult> CheckFeasibilityAsync(string address, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(address))
        {
            return new FeasibilityResult
            {
                Address = address,
                Error = "Address cannot be empty",
                CheckedAt = DateTime.Now
            };
        }

        // Check cache first
        var cachedResult = await _cacheService.GetAsync<FeasibilityResult>(address);
        if (cachedResult != null)
        {
            _logger.LogInformation("Retrieved feasibility result from cache for address: {Address}", address);
            cachedResult.FromCache = true;
            return cachedResult;
        }

        // Simulate API delay
        await Task.Delay(_random.Next(500, 2000), cancellationToken);

        var result = GenerateRealisticResult(address);
        
        // Cache successful results
        if (string.IsNullOrEmpty(result.Error))
        {
            await _cacheService.SetAsync(address, result);
        }

        _logger.LogInformation("Generated demo feasibility result for address: {Address}", address);
        return result;
    }

    public async Task<List<FeasibilityResult>> CheckMultipleFeasibilityAsync(
        IEnumerable<string> addresses, 
        IProgress<int>? progress = null, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<FeasibilityResult>();
        var addressList = addresses.ToList();
        var completed = 0;

        foreach (var address in addressList)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            try
            {
                var result = await CheckFeasibilityAsync(address, cancellationToken);
                results.Add(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feasibility for address: {Address}", address);
                results.Add(new FeasibilityResult
                {
                    Address = address,
                    Error = ex.Message,
                    CheckedAt = DateTime.Now
                });
            }

            completed++;
            progress?.Report(completed);

            // Small delay between requests
            if (completed < addressList.Count)
            {
                await Task.Delay(200, cancellationToken);
            }
        }

        return results;
    }

    private FeasibilityResult GenerateRealisticResult(string address)
    {
        var result = new FeasibilityResult
        {
            Address = address,
            CheckedAt = DateTime.Now
        };

        // Simulate various error scenarios (5% chance)
        if (_random.NextDouble() < 0.05)
        {
            var errors = new[]
            {
                "Address not found in database",
                "Invalid address format",
                "Service temporarily unavailable",
                "Address outside service area"
            };
            result.Error = errors[_random.Next(errors.Length)];
            return result;
        }

        // Determine feasibility based on address content
        var addressLower = address.ToLowerInvariant();
        var feasible = DetermineFeasibility(addressLower);

        result.Feasible = feasible;

        if (feasible)
        {
            // Generate realistic FNO and provider
            var fno = _fnoProviders[_random.Next(_fnoProviders.Length)];
            result.Fno = fno;
            
            // Some FNOs have specific provider patterns
            result.Provider = fno switch
            {
                "Openserve" => "Telkom",
                "Vumatel" => "Various ISPs",
                "Frogfoot" => "Frogfoot Networks",
                "MetroFibre" => "MetroFibre Networx",
                _ => fno
            };

            result.Message = GeneratePositiveMessage(fno);
        }
        else
        {
            result.Message = GenerateNegativeMessage();
        }

        return result;
    }

    private bool DetermineFeasibility(string addressLower)
    {
        // Check for specific area patterns
        foreach (var area in _areaFeasibility)
        {
            if (addressLower.Contains(area.Key))
            {
                return area.Value;
            }
        }

        // Check for positive indicators
        var positiveIndicators = new[] { "street", "avenue", "road", "drive", "close", "crescent" };
        var hasPositiveIndicator = positiveIndicators.Any(indicator => addressLower.Contains(indicator));

        // Check for negative indicators
        var negativeIndicators = new[] { "farm", "rural", "informal", "township" };
        var hasNegativeIndicator = negativeIndicators.Any(indicator => addressLower.Contains(indicator));

        if (hasNegativeIndicator)
            return false;

        if (hasPositiveIndicator)
            return _random.NextDouble() > 0.2; // 80% chance if it looks like a proper address

        // Default random with 70% feasibility rate
        return _random.NextDouble() > 0.3;
    }

    private string GeneratePositiveMessage(string fno)
    {
        var messages = new[]
        {
            $"Fibre available via {fno}",
            $"{fno} infrastructure confirmed",
            $"Multiple speed options available",
            $"Installation available within 2-4 weeks",
            $"Existing {fno} infrastructure in area"
        };
        return messages[_random.Next(messages.Length)];
    }

    private string GenerateNegativeMessage()
    {
        var messages = new[]
        {
            "No fibre infrastructure in area",
            "Area not yet covered by fibre networks",
            "Infrastructure planned for future rollout",
            "Alternative connectivity options may be available",
            "Contact local ISPs for wireless options"
        };
        return messages[_random.Next(messages.Length)];
    }
}
