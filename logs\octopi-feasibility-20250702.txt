2025-07-02 05:49:48.462 +02:00 [WRN] Google Places API not configured. Address autocomplete will not be available.
2025-07-02 05:50:11.617 +02:00 [INF] Checking feasibility for address: 3 boekenhout str, west acres, mbombela (Attempt 1/3)
2025-07-02 05:50:11.704 +02:00 [INF] Start processing HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:11.705 +02:00 [INF] Sending HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:19.882 +02:00 [INF] Received HTTP response headers after 8171.9664ms - 200
2025-07-02 05:50:19.883 +02:00 [INF] End processing HTTP request after 8185.7975ms - 200
2025-07-02 05:50:19.892 +02:00 [ERR] Error checking feasibility for address: 3 boekenhout str, west acres, mbombela (Attempt 1/3)
Newtonsoft.Json.JsonReaderException: Unexpected character encountered while parsing value: <. Path '', line 0, position 0.
   at Newtonsoft.Json.JsonTextReader.ParseValue()
   at Newtonsoft.Json.JsonTextReader.Read()
   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)
   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)
   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value)
   at OctopiFeasibilityChecker.Services.OctopiService.CheckFeasibilityWithRetryAsync(String address, CancellationToken cancellationToken) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\OctopiService.cs:line 134
2025-07-02 05:50:19.907 +02:00 [INF] Retrying in 2 seconds...
2025-07-02 05:50:21.920 +02:00 [INF] Checking feasibility for address: 3 boekenhout str, west acres, mbombela (Attempt 2/3)
2025-07-02 05:50:21.920 +02:00 [INF] Start processing HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:21.921 +02:00 [INF] Sending HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:22.148 +02:00 [INF] Received HTTP response headers after 227.4635ms - 200
2025-07-02 05:50:22.148 +02:00 [INF] End processing HTTP request after 227.7125ms - 200
2025-07-02 05:50:22.151 +02:00 [ERR] Error checking feasibility for address: 3 boekenhout str, west acres, mbombela (Attempt 2/3)
Newtonsoft.Json.JsonReaderException: Unexpected character encountered while parsing value: <. Path '', line 0, position 0.
   at Newtonsoft.Json.JsonTextReader.ParseValue()
   at Newtonsoft.Json.JsonTextReader.Read()
   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)
   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)
   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value)
   at OctopiFeasibilityChecker.Services.OctopiService.CheckFeasibilityWithRetryAsync(String address, CancellationToken cancellationToken) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\OctopiService.cs:line 134
2025-07-02 05:50:22.151 +02:00 [INF] Retrying in 4 seconds...
2025-07-02 05:50:26.161 +02:00 [INF] Checking feasibility for address: 3 boekenhout str, west acres, mbombela (Attempt 3/3)
2025-07-02 05:50:26.162 +02:00 [INF] Start processing HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:26.162 +02:00 [INF] Sending HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:26.380 +02:00 [INF] Received HTTP response headers after 218.1043ms - 200
2025-07-02 05:50:26.380 +02:00 [INF] End processing HTTP request after 218.766ms - 200
2025-07-02 05:50:26.382 +02:00 [ERR] Error checking feasibility for address: 3 boekenhout str, west acres, mbombela (Attempt 3/3)
Newtonsoft.Json.JsonReaderException: Unexpected character encountered while parsing value: <. Path '', line 0, position 0.
   at Newtonsoft.Json.JsonTextReader.ParseValue()
   at Newtonsoft.Json.JsonTextReader.Read()
   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)
   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)
   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value)
   at OctopiFeasibilityChecker.Services.OctopiService.CheckFeasibilityWithRetryAsync(String address, CancellationToken cancellationToken) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\OctopiService.cs:line 134
2025-07-02 05:50:26.399 +02:00 [INF] Feasibility check completed for address: 3 boekenhout str, west acres, mbombela
2025-07-02 05:50:54.048 +02:00 [INF] Checking feasibility for address: 6 Russell St, Mbombela, 1201 (Attempt 1/3)
2025-07-02 05:50:54.048 +02:00 [INF] Start processing HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:54.049 +02:00 [INF] Sending HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:54.291 +02:00 [INF] Received HTTP response headers after 242.7646ms - 200
2025-07-02 05:50:54.291 +02:00 [INF] End processing HTTP request after 243.0398ms - 200
2025-07-02 05:50:54.293 +02:00 [ERR] Error checking feasibility for address: 6 Russell St, Mbombela, 1201 (Attempt 1/3)
Newtonsoft.Json.JsonReaderException: Unexpected character encountered while parsing value: <. Path '', line 0, position 0.
   at Newtonsoft.Json.JsonTextReader.ParseValue()
   at Newtonsoft.Json.JsonTextReader.Read()
   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)
   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)
   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value)
   at OctopiFeasibilityChecker.Services.OctopiService.CheckFeasibilityWithRetryAsync(String address, CancellationToken cancellationToken) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\OctopiService.cs:line 134
2025-07-02 05:50:54.293 +02:00 [INF] Retrying in 2 seconds...
2025-07-02 05:50:56.299 +02:00 [INF] Checking feasibility for address: 6 Russell St, Mbombela, 1201 (Attempt 2/3)
2025-07-02 05:50:56.299 +02:00 [INF] Start processing HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:56.300 +02:00 [INF] Sending HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:50:56.510 +02:00 [INF] Received HTTP response headers after 210.4928ms - 200
2025-07-02 05:50:56.510 +02:00 [INF] End processing HTTP request after 210.9007ms - 200
2025-07-02 05:50:56.511 +02:00 [ERR] Error checking feasibility for address: 6 Russell St, Mbombela, 1201 (Attempt 2/3)
Newtonsoft.Json.JsonReaderException: Unexpected character encountered while parsing value: <. Path '', line 0, position 0.
   at Newtonsoft.Json.JsonTextReader.ParseValue()
   at Newtonsoft.Json.JsonTextReader.Read()
   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)
   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)
   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value)
   at OctopiFeasibilityChecker.Services.OctopiService.CheckFeasibilityWithRetryAsync(String address, CancellationToken cancellationToken) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\OctopiService.cs:line 134
2025-07-02 05:50:56.512 +02:00 [INF] Retrying in 4 seconds...
2025-07-02 05:51:00.514 +02:00 [INF] Checking feasibility for address: 6 Russell St, Mbombela, 1201 (Attempt 3/3)
2025-07-02 05:51:00.514 +02:00 [INF] Start processing HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:51:00.514 +02:00 [INF] Sending HTTP request POST "https://octopi.28east.co.za/api/feasibility"
2025-07-02 05:51:00.707 +02:00 [INF] Received HTTP response headers after 193.1428ms - 200
2025-07-02 05:51:00.707 +02:00 [INF] End processing HTTP request after 193.426ms - 200
2025-07-02 05:51:00.709 +02:00 [ERR] Error checking feasibility for address: 6 Russell St, Mbombela, 1201 (Attempt 3/3)
Newtonsoft.Json.JsonReaderException: Unexpected character encountered while parsing value: <. Path '', line 0, position 0.
   at Newtonsoft.Json.JsonTextReader.ParseValue()
   at Newtonsoft.Json.JsonTextReader.Read()
   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)
   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)
   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value)
   at OctopiFeasibilityChecker.Services.OctopiService.CheckFeasibilityWithRetryAsync(String address, CancellationToken cancellationToken) in C:\MyDocuments\ConradC\OWN Builds\Octopi-Feasibility\Services\OctopiService.cs:line 134
2025-07-02 05:51:00.713 +02:00 [INF] Feasibility check completed for address: 6 Russell St, Mbombela, 1201
2025-07-02 06:12:22.934 +02:00 [WRN] Google Places API not configured. Address autocomplete will not be available.
2025-07-02 06:13:11.293 +02:00 [INF] Generated demo feasibility result for address: 3 boekenhout str, west acres, mbombela
2025-07-02 06:13:11.308 +02:00 [INF] Feasibility check completed for address: 3 boekenhout str, west acres, mbombela
2025-07-02 06:29:05.986 +02:00 [WRN] Google Places API not configured. Address autocomplete will not be available.
2025-07-02 06:29:11.192 +02:00 [INF] Saved 2 API providers to configuration
