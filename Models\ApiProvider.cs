using Newtonsoft.Json;

namespace OctopiFeasibilityChecker.Models;

public class ApiProvider
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = string.Empty;
    public string FeasibilityEndpoint { get; set; } = string.Empty;
    public string? ApiKey { get; set; }
    public string? AuthHeader { get; set; }
    public int TimeoutSeconds { get; set; } = 30;
    public int MaxRetryAttempts { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
    public bool IsEnabled { get; set; } = true;
    public bool IsDefault { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? LastTestedAt { get; set; }
    public bool? LastTestSuccessful { get; set; }
    public string? LastTestError { get; set; }

    // Request/Response format settings
    public string RequestMethod { get; set; } = "POST";
    public string ContentType { get; set; } = "application/json";
    public string RequestBodyTemplate { get; set; } = "{\"address\":\"{ADDRESS}\"}";
    
    // Response mapping
    public string FeasibleField { get; set; } = "feasible";
    public string FnoField { get; set; } = "fno";
    public string ProviderField { get; set; } = "provider";
    public string MessageField { get; set; } = "message";
    public string ErrorField { get; set; } = "error";
}

public class GoogleApiSettings
{
    public string ApiKey { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = false;
    public string BaseUrl { get; set; } = "https://maps.googleapis.com/maps/api/place";
    public string AutocompleteEndpoint { get; set; } = "/autocomplete/json";
    public string CountryRestriction { get; set; } = "za";
    public string PlaceTypes { get; set; } = "address";
}

public class AppSettings
{
    public bool UseDemo { get; set; } = true;
    public string DefaultApiProviderId { get; set; } = string.Empty;
    public int CacheExpirationMinutes { get; set; } = 60;
    public int MaxCacheItems { get; set; } = 1000;
    public string ExportDirectory { get; set; } = "exports";
    public string DateFormat { get; set; } = "yyyy-MM-dd_HH-mm-ss";
}
