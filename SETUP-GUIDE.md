# Quick Setup Guide

## Prerequisites

**None!** The application is self-contained and doesn't require .NET to be installed.

## Quick Start

1. **Download/Clone the project**
2. **Build the standalone executable:**
   ```
   Double-click: build-exe.bat
   ```
3. **Run the application:**
   ```
   Double-click: run-app.bat
   OR
   Double-click: publish\OctopiFeasibilityChecker.exe
   ```

## Alternative (Developer Setup)

If you want to modify the code:
1. **Install .NET 6.0 SDK**
2. **Build and run:**
   ```
   dotnet build
   dotnet run
   ```

## Basic Configuration

### Required: Octopi API
The application is pre-configured to use the Octopi API at `https://octopi.28east.co.za/api/feasibility`. No additional configuration needed.

### Optional: Google Places API (for address autocomplete)

1. **Get a Google Places API Key:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing
   - Enable "Places API"
   - Create credentials (API Key)
   - Restrict the key to "Places API" for security

2. **Configure the API Key:**
   - Open `appsettings.json`
   - Replace `YOUR_GOOGLE_PLACES_API_KEY_HERE` with your actual API key
   - Save the file

3. **Test autocomplete:**
   - Start typing an address in the application
   - You should see suggestions appear after 3+ characters

## Testing the Application

1. **Single Address Test:**
   - Enter: `6 Russell St, Mbombela, 1201`
   - Click "Check FNO"
   - Should return feasibility information

2. **Batch Test:**
   - Click "Load Address File"
   - Select `sample-addresses.txt` or `sample-addresses.csv`
   - Confirm batch processing
   - Watch progress and results

3. **Export Test:**
   - After checking some addresses
   - Click "Export CSV"
   - Choose save location
   - Verify CSV file contains results

## Troubleshooting

### Build Issues
- Ensure .NET 6.0 SDK is installed
- Run `dotnet --version` to verify

### Runtime Issues
- Check logs in `logs/` folder
- Verify internet connection
- Check Octopi API status

### Google Places Issues
- Verify API key is correct
- Check billing is enabled in Google Cloud
- Ensure Places API is enabled

## File Locations

- **Configuration:** `appsettings.json`
- **Logs:** `logs/octopi-feasibility-YYYY-MM-DD.txt`
- **Exports:** `exports/` folder (created automatically)
- **Sample Files:** `sample-addresses.txt`, `sample-addresses.csv`

## Support

Check the main `README.md` for detailed documentation and troubleshooting.
