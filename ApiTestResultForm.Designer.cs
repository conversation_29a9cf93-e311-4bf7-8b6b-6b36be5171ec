namespace OctopiFeasibilityChecker
{
    partial class ApiTestResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblStatus = new Label();
            this.lblMessage = new Label();
            this.lblResponseTimeLabel = new Label();
            this.lblResponseTime = new Label();
            this.lblStatusCodeLabel = new Label();
            this.lblStatusCode = new Label();
            this.lblTestedAtLabel = new Label();
            this.lblTestedAt = new Label();
            this.tabControl = new TabControl();
            this.tabSummary = new TabPage();
            this.txtDetails = new TextBox();
            this.tabErrorDetails = new TabPage();
            this.lblErrorDetails = new Label();
            this.txtErrorDetails = new TextBox();
            this.tabResponse = new TabPage();
            this.lblResponseContent = new Label();
            this.txtResponseContent = new TextBox();
            this.btnClose = new Button();
            this.btnCopyReport = new Button();
            this.btnRetryTest = new Button();
            this.tabControl.SuspendLayout();
            this.tabSummary.SuspendLayout();
            this.tabErrorDetails.SuspendLayout();
            this.tabResponse.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblStatus.Location = new Point(20, 20);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(80, 25);
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "STATUS";
            
            // 
            // lblMessage
            // 
            this.lblMessage.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.lblMessage.Font = new Font("Segoe UI", 10F);
            this.lblMessage.Location = new Point(20, 55);
            this.lblMessage.Name = "lblMessage";
            this.lblMessage.Size = new Size(560, 40);
            this.lblMessage.TabIndex = 1;
            this.lblMessage.Text = "Test message";
            
            // 
            // lblResponseTimeLabel
            // 
            this.lblResponseTimeLabel.AutoSize = true;
            this.lblResponseTimeLabel.Location = new Point(20, 105);
            this.lblResponseTimeLabel.Name = "lblResponseTimeLabel";
            this.lblResponseTimeLabel.Size = new Size(89, 15);
            this.lblResponseTimeLabel.TabIndex = 2;
            this.lblResponseTimeLabel.Text = "Response Time:";
            
            // 
            // lblResponseTime
            // 
            this.lblResponseTime.AutoSize = true;
            this.lblResponseTime.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblResponseTime.Location = new Point(115, 105);
            this.lblResponseTime.Name = "lblResponseTime";
            this.lblResponseTime.Size = new Size(35, 15);
            this.lblResponseTime.TabIndex = 3;
            this.lblResponseTime.Text = "0 ms";
            
            // 
            // lblStatusCodeLabel
            // 
            this.lblStatusCodeLabel.AutoSize = true;
            this.lblStatusCodeLabel.Location = new Point(200, 105);
            this.lblStatusCodeLabel.Name = "lblStatusCodeLabel";
            this.lblStatusCodeLabel.Size = new Size(73, 15);
            this.lblStatusCodeLabel.TabIndex = 4;
            this.lblStatusCodeLabel.Text = "Status Code:";
            
            // 
            // lblStatusCode
            // 
            this.lblStatusCode.AutoSize = true;
            this.lblStatusCode.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblStatusCode.Location = new Point(279, 105);
            this.lblStatusCode.Name = "lblStatusCode";
            this.lblStatusCode.Size = new Size(21, 15);
            this.lblStatusCode.TabIndex = 5;
            this.lblStatusCode.Text = "---";
            
            // 
            // lblTestedAtLabel
            // 
            this.lblTestedAtLabel.AutoSize = true;
            this.lblTestedAtLabel.Location = new Point(350, 105);
            this.lblTestedAtLabel.Name = "lblTestedAtLabel";
            this.lblTestedAtLabel.Size = new Size(60, 15);
            this.lblTestedAtLabel.TabIndex = 6;
            this.lblTestedAtLabel.Text = "Tested At:";
            
            // 
            // lblTestedAt
            // 
            this.lblTestedAt.AutoSize = true;
            this.lblTestedAt.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTestedAt.Location = new Point(416, 105);
            this.lblTestedAt.Name = "lblTestedAt";
            this.lblTestedAt.Size = new Size(21, 15);
            this.lblTestedAt.TabIndex = 7;
            this.lblTestedAt.Text = "---";
            
            // 
            // tabControl
            // 
            this.tabControl.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.tabControl.Controls.Add(this.tabSummary);
            this.tabControl.Controls.Add(this.tabErrorDetails);
            this.tabControl.Controls.Add(this.tabResponse);
            this.tabControl.Location = new Point(20, 135);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new Size(560, 300);
            this.tabControl.TabIndex = 8;
            
            // 
            // tabSummary
            // 
            this.tabSummary.Controls.Add(this.txtDetails);
            this.tabSummary.Location = new Point(4, 24);
            this.tabSummary.Name = "tabSummary";
            this.tabSummary.Padding = new Padding(3);
            this.tabSummary.Size = new Size(552, 272);
            this.tabSummary.TabIndex = 0;
            this.tabSummary.Text = "Summary";
            this.tabSummary.UseVisualStyleBackColor = true;
            
            // 
            // txtDetails
            // 
            this.txtDetails.Dock = DockStyle.Fill;
            this.txtDetails.Font = new Font("Consolas", 9F);
            this.txtDetails.Location = new Point(3, 3);
            this.txtDetails.Multiline = true;
            this.txtDetails.Name = "txtDetails";
            this.txtDetails.ReadOnly = true;
            this.txtDetails.ScrollBars = ScrollBars.Vertical;
            this.txtDetails.Size = new Size(546, 266);
            this.txtDetails.TabIndex = 0;
            
            // 
            // tabErrorDetails
            // 
            this.tabErrorDetails.Controls.Add(this.lblErrorDetails);
            this.tabErrorDetails.Controls.Add(this.txtErrorDetails);
            this.tabErrorDetails.Location = new Point(4, 24);
            this.tabErrorDetails.Name = "tabErrorDetails";
            this.tabErrorDetails.Padding = new Padding(3);
            this.tabErrorDetails.Size = new Size(552, 272);
            this.tabErrorDetails.TabIndex = 1;
            this.tabErrorDetails.Text = "Error Details";
            this.tabErrorDetails.UseVisualStyleBackColor = true;
            
            // 
            // lblErrorDetails
            // 
            this.lblErrorDetails.AutoSize = true;
            this.lblErrorDetails.Location = new Point(6, 10);
            this.lblErrorDetails.Name = "lblErrorDetails";
            this.lblErrorDetails.Size = new Size(73, 15);
            this.lblErrorDetails.TabIndex = 1;
            this.lblErrorDetails.Text = "Error Details:";
            
            // 
            // txtErrorDetails
            // 
            this.txtErrorDetails.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.txtErrorDetails.Font = new Font("Consolas", 9F);
            this.txtErrorDetails.Location = new Point(6, 28);
            this.txtErrorDetails.Multiline = true;
            this.txtErrorDetails.Name = "txtErrorDetails";
            this.txtErrorDetails.ReadOnly = true;
            this.txtErrorDetails.ScrollBars = ScrollBars.Vertical;
            this.txtErrorDetails.Size = new Size(540, 238);
            this.txtErrorDetails.TabIndex = 0;
            
            // 
            // tabResponse
            // 
            this.tabResponse.Controls.Add(this.lblResponseContent);
            this.tabResponse.Controls.Add(this.txtResponseContent);
            this.tabResponse.Location = new Point(4, 24);
            this.tabResponse.Name = "tabResponse";
            this.tabResponse.Size = new Size(552, 272);
            this.tabResponse.TabIndex = 2;
            this.tabResponse.Text = "Response Content";
            this.tabResponse.UseVisualStyleBackColor = true;
            
            // 
            // lblResponseContent
            // 
            this.lblResponseContent.AutoSize = true;
            this.lblResponseContent.Location = new Point(6, 10);
            this.lblResponseContent.Name = "lblResponseContent";
            this.lblResponseContent.Size = new Size(105, 15);
            this.lblResponseContent.TabIndex = 1;
            this.lblResponseContent.Text = "Response Content:";
            
            // 
            // txtResponseContent
            // 
            this.txtResponseContent.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.txtResponseContent.Font = new Font("Consolas", 9F);
            this.txtResponseContent.Location = new Point(6, 28);
            this.txtResponseContent.Multiline = true;
            this.txtResponseContent.Name = "txtResponseContent";
            this.txtResponseContent.ReadOnly = true;
            this.txtResponseContent.ScrollBars = ScrollBars.Both;
            this.txtResponseContent.Size = new Size(540, 238);
            this.txtResponseContent.TabIndex = 0;
            
            // 
            // btnClose
            // 
            this.btnClose.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnClose.Location = new Point(505, 450);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(75, 30);
            this.btnClose.TabIndex = 9;
            this.btnClose.Text = "Close";
            this.btnClose.UseVisualStyleBackColor = true;
            this.btnClose.Click += this.btnClose_Click;
            
            // 
            // btnCopyReport
            // 
            this.btnCopyReport.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnCopyReport.Location = new Point(20, 450);
            this.btnCopyReport.Name = "btnCopyReport";
            this.btnCopyReport.Size = new Size(100, 30);
            this.btnCopyReport.TabIndex = 10;
            this.btnCopyReport.Text = "Copy Report";
            this.btnCopyReport.UseVisualStyleBackColor = true;
            this.btnCopyReport.Click += this.btnCopyReport_Click;
            
            // 
            // btnRetryTest
            // 
            this.btnRetryTest.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnRetryTest.Location = new Point(130, 450);
            this.btnRetryTest.Name = "btnRetryTest";
            this.btnRetryTest.Size = new Size(100, 30);
            this.btnRetryTest.TabIndex = 11;
            this.btnRetryTest.Text = "Retry Test";
            this.btnRetryTest.UseVisualStyleBackColor = true;
            this.btnRetryTest.Click += this.btnRetryTest_Click;
            
            // 
            // ApiTestResultForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 500);
            this.Controls.Add(this.btnRetryTest);
            this.Controls.Add(this.btnCopyReport);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(this.lblTestedAt);
            this.Controls.Add(this.lblTestedAtLabel);
            this.Controls.Add(this.lblStatusCode);
            this.Controls.Add(this.lblStatusCodeLabel);
            this.Controls.Add(this.lblResponseTime);
            this.Controls.Add(this.lblResponseTimeLabel);
            this.Controls.Add(this.lblMessage);
            this.Controls.Add(this.lblStatus);
            this.MinimumSize = new Size(600, 500);
            this.Name = "ApiTestResultForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "API Test Result";
            this.tabControl.ResumeLayout(false);
            this.tabSummary.ResumeLayout(false);
            this.tabSummary.PerformLayout();
            this.tabErrorDetails.ResumeLayout(false);
            this.tabErrorDetails.PerformLayout();
            this.tabResponse.ResumeLayout(false);
            this.tabResponse.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private Label lblStatus;
        private Label lblMessage;
        private Label lblResponseTimeLabel;
        private Label lblResponseTime;
        private Label lblStatusCodeLabel;
        private Label lblStatusCode;
        private Label lblTestedAtLabel;
        private Label lblTestedAt;
        private TabControl tabControl;
        private TabPage tabSummary;
        private TextBox txtDetails;
        private TabPage tabErrorDetails;
        private Label lblErrorDetails;
        private TextBox txtErrorDetails;
        private TabPage tabResponse;
        private Label lblResponseContent;
        private TextBox txtResponseContent;
        private Button btnClose;
        private Button btnCopyReport;
        private Button btnRetryTest;
    }
}
