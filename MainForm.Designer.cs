namespace OctopiFeasibilityChecker
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblAddress = new Label();
            this.txtAddress = new TextBox();
            this.btnCheckFno = new Button();
            this.lblResult = new Label();
            this.txtResult = new TextBox();
            this.groupBoxSingle = new GroupBox();
            this.groupBoxBatch = new GroupBox();
            this.btnLoadFile = new Button();
            this.lblFileInfo = new Label();
            this.groupBoxResults = new GroupBox();
            this.lstResults = new ListView();
            this.colAddress = new ColumnHeader();
            this.colFeasible = new ColumnHeader();
            this.colFno = new ColumnHeader();
            this.colProvider = new ColumnHeader();
            this.colCheckedAt = new ColumnHeader();
            this.colFromCache = new ColumnHeader();
            this.colMessage = new ColumnHeader();
            this.btnExportCsv = new Button();
            this.btnClearResults = new Button();
            this.statusStrip = new StatusStrip();
            this.lblStatus = new ToolStripStatusLabel();
            this.lblResultCount = new ToolStripStatusLabel();
            this.lblCacheCount = new ToolStripStatusLabel();
            this.progressBar = new ProgressBar();
            this.btnCancel = new Button();
            this.btnClearCache = new Button();
            this.groupBoxSingle.SuspendLayout();
            this.groupBoxBatch.SuspendLayout();
            this.groupBoxResults.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // lblAddress
            // 
            this.lblAddress.AutoSize = true;
            this.lblAddress.Location = new Point(12, 25);
            this.lblAddress.Name = "lblAddress";
            this.lblAddress.Size = new Size(58, 15);
            this.lblAddress.TabIndex = 0;
            this.lblAddress.Text = "Address:";
            
            // 
            // txtAddress
            // 
            this.txtAddress.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtAddress.Location = new Point(76, 22);
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.PlaceholderText = "e.g., 6 Russell St, Mbombela, 1201";
            this.txtAddress.Size = new Size(400, 23);
            this.txtAddress.TabIndex = 1;
            
            // 
            // btnCheckFno
            // 
            this.btnCheckFno.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            this.btnCheckFno.Location = new Point(482, 21);
            this.btnCheckFno.Name = "btnCheckFno";
            this.btnCheckFno.Size = new Size(100, 25);
            this.btnCheckFno.TabIndex = 2;
            this.btnCheckFno.Text = "Check FNO";
            this.btnCheckFno.UseVisualStyleBackColor = true;
            this.btnCheckFno.Click += this.btnCheckFno_Click;
            
            // 
            // lblResult
            // 
            this.lblResult.AutoSize = true;
            this.lblResult.Location = new Point(12, 55);
            this.lblResult.Name = "lblResult";
            this.lblResult.Size = new Size(42, 15);
            this.lblResult.TabIndex = 3;
            this.lblResult.Text = "Result:";
            
            // 
            // txtResult
            // 
            this.txtResult.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtResult.Location = new Point(76, 52);
            this.txtResult.Multiline = true;
            this.txtResult.Name = "txtResult";
            this.txtResult.ReadOnly = true;
            this.txtResult.Size = new Size(506, 40);
            this.txtResult.TabIndex = 4;
            
            // 
            // groupBoxSingle
            // 
            this.groupBoxSingle.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.groupBoxSingle.Controls.Add(this.lblAddress);
            this.groupBoxSingle.Controls.Add(this.txtAddress);
            this.groupBoxSingle.Controls.Add(this.btnCheckFno);
            this.groupBoxSingle.Controls.Add(this.lblResult);
            this.groupBoxSingle.Controls.Add(this.txtResult);
            this.groupBoxSingle.Location = new Point(12, 12);
            this.groupBoxSingle.Name = "groupBoxSingle";
            this.groupBoxSingle.Size = new Size(600, 105);
            this.groupBoxSingle.TabIndex = 5;
            this.groupBoxSingle.TabStop = false;
            this.groupBoxSingle.Text = "Single Address Check";
            
            // 
            // groupBoxBatch
            // 
            this.groupBoxBatch.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.groupBoxBatch.Controls.Add(this.btnLoadFile);
            this.groupBoxBatch.Controls.Add(this.lblFileInfo);
            this.groupBoxBatch.Location = new Point(12, 123);
            this.groupBoxBatch.Name = "groupBoxBatch";
            this.groupBoxBatch.Size = new Size(600, 55);
            this.groupBoxBatch.TabIndex = 6;
            this.groupBoxBatch.TabStop = false;
            this.groupBoxBatch.Text = "Batch Processing";
            
            // 
            // btnLoadFile
            // 
            this.btnLoadFile.Location = new Point(12, 22);
            this.btnLoadFile.Name = "btnLoadFile";
            this.btnLoadFile.Size = new Size(120, 25);
            this.btnLoadFile.TabIndex = 0;
            this.btnLoadFile.Text = "Load Address File";
            this.btnLoadFile.UseVisualStyleBackColor = true;
            this.btnLoadFile.Click += this.btnLoadFile_Click;
            
            // 
            // lblFileInfo
            // 
            this.lblFileInfo.AutoSize = true;
            this.lblFileInfo.Location = new Point(138, 27);
            this.lblFileInfo.Name = "lblFileInfo";
            this.lblFileInfo.Size = new Size(200, 15);
            this.lblFileInfo.TabIndex = 1;
            this.lblFileInfo.Text = "Supports .txt and .csv files";
            
            // 
            // groupBoxResults
            // 
            this.groupBoxResults.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.groupBoxResults.Controls.Add(this.lstResults);
            this.groupBoxResults.Controls.Add(this.btnExportCsv);
            this.groupBoxResults.Controls.Add(this.btnClearResults);
            this.groupBoxResults.Controls.Add(this.btnClearCache);
            this.groupBoxResults.Location = new Point(12, 184);
            this.groupBoxResults.Name = "groupBoxResults";
            this.groupBoxResults.Size = new Size(600, 300);
            this.groupBoxResults.TabIndex = 7;
            this.groupBoxResults.TabStop = false;
            this.groupBoxResults.Text = "Results";
            
            // 
            // lstResults
            // 
            this.lstResults.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.lstResults.FullRowSelect = true;
            this.lstResults.GridLines = true;
            this.lstResults.Location = new Point(12, 22);
            this.lstResults.Name = "lstResults";
            this.lstResults.Size = new Size(576, 235);
            this.lstResults.TabIndex = 0;
            this.lstResults.UseCompatibleStateImageBehavior = false;
            this.lstResults.View = View.Details;
            this.lstResults.Columns.Add(this.colAddress);
            this.lstResults.Columns.Add(this.colFeasible);
            this.lstResults.Columns.Add(this.colFno);
            this.lstResults.Columns.Add(this.colProvider);
            this.lstResults.Columns.Add(this.colCheckedAt);
            this.lstResults.Columns.Add(this.colFromCache);
            this.lstResults.Columns.Add(this.colMessage);
            
            // 
            // colAddress
            // 
            this.colAddress.Text = "Address";
            this.colAddress.Width = 200;
            
            // 
            // colFeasible
            // 
            this.colFeasible.Text = "Feasible";
            this.colFeasible.Width = 60;
            
            // 
            // colFno
            // 
            this.colFno.Text = "FNO";
            this.colFno.Width = 80;
            
            // 
            // colProvider
            // 
            this.colProvider.Text = "Provider";
            this.colProvider.Width = 80;
            
            // 
            // colCheckedAt
            // 
            this.colCheckedAt.Text = "Checked At";
            this.colCheckedAt.Width = 120;
            
            // 
            // colFromCache
            // 
            this.colFromCache.Text = "Cache";
            this.colFromCache.Width = 50;
            
            // 
            // colMessage
            // 
            this.colMessage.Text = "Message/Error";
            this.colMessage.Width = 150;
            
            // 
            // btnExportCsv
            // 
            this.btnExportCsv.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnExportCsv.Location = new Point(12, 265);
            this.btnExportCsv.Name = "btnExportCsv";
            this.btnExportCsv.Size = new Size(100, 25);
            this.btnExportCsv.TabIndex = 1;
            this.btnExportCsv.Text = "Export CSV";
            this.btnExportCsv.UseVisualStyleBackColor = true;
            this.btnExportCsv.Click += this.btnExportCsv_Click;
            
            // 
            // btnClearResults
            // 
            this.btnClearResults.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnClearResults.Location = new Point(118, 265);
            this.btnClearResults.Name = "btnClearResults";
            this.btnClearResults.Size = new Size(100, 25);
            this.btnClearResults.TabIndex = 2;
            this.btnClearResults.Text = "Clear Results";
            this.btnClearResults.UseVisualStyleBackColor = true;
            this.btnClearResults.Click += this.btnClearResults_Click;
            
            // 
            // btnClearCache
            // 
            this.btnClearCache.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnClearCache.Location = new Point(224, 265);
            this.btnClearCache.Name = "btnClearCache";
            this.btnClearCache.Size = new Size(100, 25);
            this.btnClearCache.TabIndex = 3;
            this.btnClearCache.Text = "Clear Cache";
            this.btnClearCache.UseVisualStyleBackColor = true;
            this.btnClearCache.Click += this.btnClearCache_Click;
            
            // 
            // progressBar
            // 
            this.progressBar.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.progressBar.Location = new Point(12, 490);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new Size(500, 23);
            this.progressBar.TabIndex = 8;
            this.progressBar.Visible = false;
            
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnCancel.Location = new Point(518, 490);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(75, 23);
            this.btnCancel.TabIndex = 9;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += this.btnCancel_Click;
            
            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.lblStatus,
                this.lblResultCount,
                this.lblCacheCount});
            this.statusStrip.Location = new Point(0, 519);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(624, 22);
            this.statusStrip.TabIndex = 10;
            this.statusStrip.Text = "statusStrip1";
            
            // 
            // lblStatus
            // 
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(39, 17);
            this.lblStatus.Text = "Ready";
            
            // 
            // lblResultCount
            // 
            this.lblResultCount.Name = "lblResultCount";
            this.lblResultCount.Size = new Size(58, 17);
            this.lblResultCount.Text = "Results: 0";
            
            // 
            // lblCacheCount
            // 
            this.lblCacheCount.Name = "lblCacheCount";
            this.lblCacheCount.Size = new Size(50, 17);
            this.lblCacheCount.Text = "Cache: 0";
            
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(624, 541);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.progressBar);
            this.Controls.Add(this.groupBoxResults);
            this.Controls.Add(this.groupBoxBatch);
            this.Controls.Add(this.groupBoxSingle);
            this.MinimumSize = new Size(640, 580);
            this.Name = "MainForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "Octopi Feasibility Checker";
            this.groupBoxSingle.ResumeLayout(false);
            this.groupBoxSingle.PerformLayout();
            this.groupBoxBatch.ResumeLayout(false);
            this.groupBoxBatch.PerformLayout();
            this.groupBoxResults.ResumeLayout(false);
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private Label lblAddress;
        private TextBox txtAddress;
        private Button btnCheckFno;
        private Label lblResult;
        private TextBox txtResult;
        private GroupBox groupBoxSingle;
        private GroupBox groupBoxBatch;
        private Button btnLoadFile;
        private Label lblFileInfo;
        private GroupBox groupBoxResults;
        private ListView lstResults;
        private ColumnHeader colAddress;
        private ColumnHeader colFeasible;
        private ColumnHeader colFno;
        private ColumnHeader colProvider;
        private ColumnHeader colCheckedAt;
        private ColumnHeader colFromCache;
        private ColumnHeader colMessage;
        private Button btnExportCsv;
        private Button btnClearResults;
        private Button btnClearCache;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel lblStatus;
        private ToolStripStatusLabel lblResultCount;
        private ToolStripStatusLabel lblCacheCount;
        private ProgressBar progressBar;
        private Button btnCancel;
    }
}
