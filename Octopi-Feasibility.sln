Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OctopiFeasibilityChecker", "OctopiFeasibilityChecker.csproj", "{44928B48-9EEF-4C8A-98FB-D30341062D09}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{44928B48-9EEF-4C8A-98FB-D30341062D09}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44928B48-9EEF-4C8A-98FB-D30341062D09}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44928B48-9EEF-4C8A-98FB-D30341062D09}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44928B48-9EEF-4C8A-98FB-D30341062D09}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C24FAC27-6746-43C8-AE1E-0E6432AB2CCB}
	EndGlobalSection
EndGlobal
