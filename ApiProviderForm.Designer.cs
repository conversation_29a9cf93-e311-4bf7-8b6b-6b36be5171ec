namespace OctopiFeasibilityChecker
{
    partial class ApiProviderForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl = new TabControl();
            this.tabBasic = new TabPage();
            this.lblName = new Label();
            this.txtName = new TextBox();
            this.lblBaseUrl = new Label();
            this.txtBaseUrl = new TextBox();
            this.lblFeasibilityEndpoint = new Label();
            this.txtFeasibilityEndpoint = new TextBox();
            this.lblApiKey = new Label();
            this.txtApiKey = new TextBox();
            this.lblAuthHeader = new Label();
            this.txtAuthHeader = new TextBox();
            this.chkIsEnabled = new CheckBox();
            this.chkIsDefault = new CheckBox();
            this.tabAdvanced = new TabPage();
            this.lblTimeoutSeconds = new Label();
            this.numTimeoutSeconds = new NumericUpDown();
            this.lblMaxRetryAttempts = new Label();
            this.numMaxRetryAttempts = new NumericUpDown();
            this.lblRetryDelaySeconds = new Label();
            this.numRetryDelaySeconds = new NumericUpDown();
            this.lblRequestMethod = new Label();
            this.cmbRequestMethod = new ComboBox();
            this.lblContentType = new Label();
            this.txtContentType = new TextBox();
            this.lblRequestBodyTemplate = new Label();
            this.txtRequestBodyTemplate = new TextBox();
            this.btnTestTemplate = new Button();
            this.tabResponseMapping = new TabPage();
            this.lblFeasibleField = new Label();
            this.txtFeasibleField = new TextBox();
            this.lblFnoField = new Label();
            this.txtFnoField = new TextBox();
            this.lblProviderField = new Label();
            this.txtProviderField = new TextBox();
            this.lblMessageField = new Label();
            this.txtMessageField = new TextBox();
            this.lblErrorField = new Label();
            this.txtErrorField = new TextBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.tabControl.SuspendLayout();
            this.tabBasic.SuspendLayout();
            this.tabAdvanced.SuspendLayout();
            this.tabResponseMapping.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeoutSeconds)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRetryAttempts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRetryDelaySeconds)).BeginInit();
            this.SuspendLayout();
            
            // 
            // tabControl
            // 
            this.tabControl.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.tabControl.Controls.Add(this.tabBasic);
            this.tabControl.Controls.Add(this.tabAdvanced);
            this.tabControl.Controls.Add(this.tabResponseMapping);
            this.tabControl.Location = new Point(12, 12);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new Size(560, 400);
            this.tabControl.TabIndex = 0;
            
            // 
            // tabBasic
            // 
            this.tabBasic.Controls.Add(this.lblName);
            this.tabBasic.Controls.Add(this.txtName);
            this.tabBasic.Controls.Add(this.lblBaseUrl);
            this.tabBasic.Controls.Add(this.txtBaseUrl);
            this.tabBasic.Controls.Add(this.lblFeasibilityEndpoint);
            this.tabBasic.Controls.Add(this.txtFeasibilityEndpoint);
            this.tabBasic.Controls.Add(this.lblApiKey);
            this.tabBasic.Controls.Add(this.txtApiKey);
            this.tabBasic.Controls.Add(this.lblAuthHeader);
            this.tabBasic.Controls.Add(this.txtAuthHeader);
            this.tabBasic.Controls.Add(this.chkIsEnabled);
            this.tabBasic.Controls.Add(this.chkIsDefault);
            this.tabBasic.Location = new Point(4, 24);
            this.tabBasic.Name = "tabBasic";
            this.tabBasic.Padding = new Padding(3);
            this.tabBasic.Size = new Size(552, 372);
            this.tabBasic.TabIndex = 0;
            this.tabBasic.Text = "Basic Settings";
            this.tabBasic.UseVisualStyleBackColor = true;
            
            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(20, 25);
            this.lblName.Name = "lblName";
            this.lblName.Size = new Size(42, 15);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "Name:";
            
            // 
            // txtName
            // 
            this.txtName.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtName.Location = new Point(150, 22);
            this.txtName.Name = "txtName";
            this.txtName.Size = new Size(380, 23);
            this.txtName.TabIndex = 1;
            
            // 
            // lblBaseUrl
            // 
            this.lblBaseUrl.AutoSize = true;
            this.lblBaseUrl.Location = new Point(20, 55);
            this.lblBaseUrl.Name = "lblBaseUrl";
            this.lblBaseUrl.Size = new Size(60, 15);
            this.lblBaseUrl.TabIndex = 2;
            this.lblBaseUrl.Text = "Base URL:";
            
            // 
            // txtBaseUrl
            // 
            this.txtBaseUrl.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtBaseUrl.Location = new Point(150, 52);
            this.txtBaseUrl.Name = "txtBaseUrl";
            this.txtBaseUrl.Size = new Size(380, 23);
            this.txtBaseUrl.TabIndex = 3;
            
            // 
            // lblFeasibilityEndpoint
            // 
            this.lblFeasibilityEndpoint.AutoSize = true;
            this.lblFeasibilityEndpoint.Location = new Point(20, 85);
            this.lblFeasibilityEndpoint.Name = "lblFeasibilityEndpoint";
            this.lblFeasibilityEndpoint.Size = new Size(111, 15);
            this.lblFeasibilityEndpoint.TabIndex = 4;
            this.lblFeasibilityEndpoint.Text = "Feasibility Endpoint:";
            
            // 
            // txtFeasibilityEndpoint
            // 
            this.txtFeasibilityEndpoint.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtFeasibilityEndpoint.Location = new Point(150, 82);
            this.txtFeasibilityEndpoint.Name = "txtFeasibilityEndpoint";
            this.txtFeasibilityEndpoint.Size = new Size(380, 23);
            this.txtFeasibilityEndpoint.TabIndex = 5;
            
            // 
            // lblApiKey
            // 
            this.lblApiKey.AutoSize = true;
            this.lblApiKey.Location = new Point(20, 115);
            this.lblApiKey.Name = "lblApiKey";
            this.lblApiKey.Size = new Size(54, 15);
            this.lblApiKey.TabIndex = 6;
            this.lblApiKey.Text = "API Key:";
            
            // 
            // txtApiKey
            // 
            this.txtApiKey.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtApiKey.Location = new Point(150, 112);
            this.txtApiKey.Name = "txtApiKey";
            this.txtApiKey.Size = new Size(380, 23);
            this.txtApiKey.TabIndex = 7;
            
            // 
            // lblAuthHeader
            // 
            this.lblAuthHeader.AutoSize = true;
            this.lblAuthHeader.Location = new Point(20, 145);
            this.lblAuthHeader.Name = "lblAuthHeader";
            this.lblAuthHeader.Size = new Size(76, 15);
            this.lblAuthHeader.TabIndex = 8;
            this.lblAuthHeader.Text = "Auth Header:";
            
            // 
            // txtAuthHeader
            // 
            this.txtAuthHeader.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtAuthHeader.Location = new Point(150, 142);
            this.txtAuthHeader.Name = "txtAuthHeader";
            this.txtAuthHeader.Size = new Size(380, 23);
            this.txtAuthHeader.TabIndex = 9;
            
            // 
            // chkIsEnabled
            // 
            this.chkIsEnabled.AutoSize = true;
            this.chkIsEnabled.Location = new Point(150, 180);
            this.chkIsEnabled.Name = "chkIsEnabled";
            this.chkIsEnabled.Size = new Size(68, 19);
            this.chkIsEnabled.TabIndex = 10;
            this.chkIsEnabled.Text = "Enabled";
            this.chkIsEnabled.UseVisualStyleBackColor = true;
            
            // 
            // chkIsDefault
            // 
            this.chkIsDefault.AutoSize = true;
            this.chkIsDefault.Location = new Point(250, 180);
            this.chkIsDefault.Name = "chkIsDefault";
            this.chkIsDefault.Size = new Size(64, 19);
            this.chkIsDefault.TabIndex = 11;
            this.chkIsDefault.Text = "Default";
            this.chkIsDefault.UseVisualStyleBackColor = true;
            
            // 
            // tabAdvanced
            // 
            this.tabAdvanced.Controls.Add(this.lblTimeoutSeconds);
            this.tabAdvanced.Controls.Add(this.numTimeoutSeconds);
            this.tabAdvanced.Controls.Add(this.lblMaxRetryAttempts);
            this.tabAdvanced.Controls.Add(this.numMaxRetryAttempts);
            this.tabAdvanced.Controls.Add(this.lblRetryDelaySeconds);
            this.tabAdvanced.Controls.Add(this.numRetryDelaySeconds);
            this.tabAdvanced.Controls.Add(this.lblRequestMethod);
            this.tabAdvanced.Controls.Add(this.cmbRequestMethod);
            this.tabAdvanced.Controls.Add(this.lblContentType);
            this.tabAdvanced.Controls.Add(this.txtContentType);
            this.tabAdvanced.Controls.Add(this.lblRequestBodyTemplate);
            this.tabAdvanced.Controls.Add(this.txtRequestBodyTemplate);
            this.tabAdvanced.Controls.Add(this.btnTestTemplate);
            this.tabAdvanced.Location = new Point(4, 24);
            this.tabAdvanced.Name = "tabAdvanced";
            this.tabAdvanced.Padding = new Padding(3);
            this.tabAdvanced.Size = new Size(552, 372);
            this.tabAdvanced.TabIndex = 1;
            this.tabAdvanced.Text = "Advanced Settings";
            this.tabAdvanced.UseVisualStyleBackColor = true;
            
            // 
            // lblTimeoutSeconds
            // 
            this.lblTimeoutSeconds.AutoSize = true;
            this.lblTimeoutSeconds.Location = new Point(20, 25);
            this.lblTimeoutSeconds.Name = "lblTimeoutSeconds";
            this.lblTimeoutSeconds.Size = new Size(96, 15);
            this.lblTimeoutSeconds.TabIndex = 0;
            this.lblTimeoutSeconds.Text = "Timeout (seconds):";
            
            // 
            // numTimeoutSeconds
            // 
            this.numTimeoutSeconds.Location = new Point(150, 23);
            this.numTimeoutSeconds.Maximum = new decimal(new int[] { 300, 0, 0, 0 });
            this.numTimeoutSeconds.Minimum = new decimal(new int[] { 5, 0, 0, 0 });
            this.numTimeoutSeconds.Name = "numTimeoutSeconds";
            this.numTimeoutSeconds.Size = new Size(80, 23);
            this.numTimeoutSeconds.TabIndex = 1;
            this.numTimeoutSeconds.Value = new decimal(new int[] { 30, 0, 0, 0 });
            
            // 
            // lblMaxRetryAttempts
            // 
            this.lblMaxRetryAttempts.AutoSize = true;
            this.lblMaxRetryAttempts.Location = new Point(20, 55);
            this.lblMaxRetryAttempts.Name = "lblMaxRetryAttempts";
            this.lblMaxRetryAttempts.Size = new Size(109, 15);
            this.lblMaxRetryAttempts.TabIndex = 2;
            this.lblMaxRetryAttempts.Text = "Max Retry Attempts:";
            
            // 
            // numMaxRetryAttempts
            // 
            this.numMaxRetryAttempts.Location = new Point(150, 53);
            this.numMaxRetryAttempts.Maximum = new decimal(new int[] { 10, 0, 0, 0 });
            this.numMaxRetryAttempts.Name = "numMaxRetryAttempts";
            this.numMaxRetryAttempts.Size = new Size(80, 23);
            this.numMaxRetryAttempts.TabIndex = 3;
            this.numMaxRetryAttempts.Value = new decimal(new int[] { 3, 0, 0, 0 });
            
            // 
            // lblRetryDelaySeconds
            // 
            this.lblRetryDelaySeconds.AutoSize = true;
            this.lblRetryDelaySeconds.Location = new Point(20, 85);
            this.lblRetryDelaySeconds.Name = "lblRetryDelaySeconds";
            this.lblRetryDelaySeconds.Size = new Size(118, 15);
            this.lblRetryDelaySeconds.TabIndex = 4;
            this.lblRetryDelaySeconds.Text = "Retry Delay (seconds):";
            
            // 
            // numRetryDelaySeconds
            // 
            this.numRetryDelaySeconds.Location = new Point(150, 83);
            this.numRetryDelaySeconds.Maximum = new decimal(new int[] { 60, 0, 0, 0 });
            this.numRetryDelaySeconds.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            this.numRetryDelaySeconds.Name = "numRetryDelaySeconds";
            this.numRetryDelaySeconds.Size = new Size(80, 23);
            this.numRetryDelaySeconds.TabIndex = 5;
            this.numRetryDelaySeconds.Value = new decimal(new int[] { 2, 0, 0, 0 });
            
            // 
            // lblRequestMethod
            // 
            this.lblRequestMethod.AutoSize = true;
            this.lblRequestMethod.Location = new Point(20, 115);
            this.lblRequestMethod.Name = "lblRequestMethod";
            this.lblRequestMethod.Size = new Size(93, 15);
            this.lblRequestMethod.TabIndex = 6;
            this.lblRequestMethod.Text = "Request Method:";
            
            // 
            // cmbRequestMethod
            // 
            this.cmbRequestMethod.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbRequestMethod.FormattingEnabled = true;
            this.cmbRequestMethod.Items.AddRange(new object[] { "GET", "POST", "PUT", "PATCH" });
            this.cmbRequestMethod.Location = new Point(150, 112);
            this.cmbRequestMethod.Name = "cmbRequestMethod";
            this.cmbRequestMethod.Size = new Size(100, 23);
            this.cmbRequestMethod.TabIndex = 7;
            
            // 
            // lblContentType
            // 
            this.lblContentType.AutoSize = true;
            this.lblContentType.Location = new Point(20, 145);
            this.lblContentType.Name = "lblContentType";
            this.lblContentType.Size = new Size(80, 15);
            this.lblContentType.TabIndex = 8;
            this.lblContentType.Text = "Content Type:";
            
            // 
            // txtContentType
            // 
            this.txtContentType.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtContentType.Location = new Point(150, 142);
            this.txtContentType.Name = "txtContentType";
            this.txtContentType.Size = new Size(380, 23);
            this.txtContentType.TabIndex = 9;
            
            // 
            // lblRequestBodyTemplate
            // 
            this.lblRequestBodyTemplate.AutoSize = true;
            this.lblRequestBodyTemplate.Location = new Point(20, 175);
            this.lblRequestBodyTemplate.Name = "lblRequestBodyTemplate";
            this.lblRequestBodyTemplate.Size = new Size(124, 15);
            this.lblRequestBodyTemplate.TabIndex = 10;
            this.lblRequestBodyTemplate.Text = "Request Body Template:";
            
            // 
            // txtRequestBodyTemplate
            // 
            this.txtRequestBodyTemplate.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.txtRequestBodyTemplate.Location = new Point(150, 172);
            this.txtRequestBodyTemplate.Multiline = true;
            this.txtRequestBodyTemplate.Name = "txtRequestBodyTemplate";
            this.txtRequestBodyTemplate.ScrollBars = ScrollBars.Vertical;
            this.txtRequestBodyTemplate.Size = new Size(380, 150);
            this.txtRequestBodyTemplate.TabIndex = 11;
            
            // 
            // btnTestTemplate
            // 
            this.btnTestTemplate.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnTestTemplate.Location = new Point(150, 330);
            this.btnTestTemplate.Name = "btnTestTemplate";
            this.btnTestTemplate.Size = new Size(100, 25);
            this.btnTestTemplate.TabIndex = 12;
            this.btnTestTemplate.Text = "Test Template";
            this.btnTestTemplate.UseVisualStyleBackColor = true;
            this.btnTestTemplate.Click += this.btnTestTemplate_Click;
            
            // 
            // tabResponseMapping
            // 
            this.tabResponseMapping.Controls.Add(this.lblFeasibleField);
            this.tabResponseMapping.Controls.Add(this.txtFeasibleField);
            this.tabResponseMapping.Controls.Add(this.lblFnoField);
            this.tabResponseMapping.Controls.Add(this.txtFnoField);
            this.tabResponseMapping.Controls.Add(this.lblProviderField);
            this.tabResponseMapping.Controls.Add(this.txtProviderField);
            this.tabResponseMapping.Controls.Add(this.lblMessageField);
            this.tabResponseMapping.Controls.Add(this.txtMessageField);
            this.tabResponseMapping.Controls.Add(this.lblErrorField);
            this.tabResponseMapping.Controls.Add(this.txtErrorField);
            this.tabResponseMapping.Location = new Point(4, 24);
            this.tabResponseMapping.Name = "tabResponseMapping";
            this.tabResponseMapping.Size = new Size(552, 372);
            this.tabResponseMapping.TabIndex = 2;
            this.tabResponseMapping.Text = "Response Mapping";
            this.tabResponseMapping.UseVisualStyleBackColor = true;
            
            // 
            // lblFeasibleField
            // 
            this.lblFeasibleField.AutoSize = true;
            this.lblFeasibleField.Location = new Point(20, 25);
            this.lblFeasibleField.Name = "lblFeasibleField";
            this.lblFeasibleField.Size = new Size(78, 15);
            this.lblFeasibleField.TabIndex = 0;
            this.lblFeasibleField.Text = "Feasible Field:";
            
            // 
            // txtFeasibleField
            // 
            this.txtFeasibleField.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtFeasibleField.Location = new Point(150, 22);
            this.txtFeasibleField.Name = "txtFeasibleField";
            this.txtFeasibleField.Size = new Size(380, 23);
            this.txtFeasibleField.TabIndex = 1;
            
            // 
            // lblFnoField
            // 
            this.lblFnoField.AutoSize = true;
            this.lblFnoField.Location = new Point(20, 55);
            this.lblFnoField.Name = "lblFnoField";
            this.lblFnoField.Size = new Size(60, 15);
            this.lblFnoField.TabIndex = 2;
            this.lblFnoField.Text = "FNO Field:";
            
            // 
            // txtFnoField
            // 
            this.txtFnoField.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtFnoField.Location = new Point(150, 52);
            this.txtFnoField.Name = "txtFnoField";
            this.txtFnoField.Size = new Size(380, 23);
            this.txtFnoField.TabIndex = 3;
            
            // 
            // lblProviderField
            // 
            this.lblProviderField.AutoSize = true;
            this.lblProviderField.Location = new Point(20, 85);
            this.lblProviderField.Name = "lblProviderField";
            this.lblProviderField.Size = new Size(82, 15);
            this.lblProviderField.TabIndex = 4;
            this.lblProviderField.Text = "Provider Field:";
            
            // 
            // txtProviderField
            // 
            this.txtProviderField.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtProviderField.Location = new Point(150, 82);
            this.txtProviderField.Name = "txtProviderField";
            this.txtProviderField.Size = new Size(380, 23);
            this.txtProviderField.TabIndex = 5;
            
            // 
            // lblMessageField
            // 
            this.lblMessageField.AutoSize = true;
            this.lblMessageField.Location = new Point(20, 115);
            this.lblMessageField.Name = "lblMessageField";
            this.lblMessageField.Size = new Size(82, 15);
            this.lblMessageField.TabIndex = 6;
            this.lblMessageField.Text = "Message Field:";
            
            // 
            // txtMessageField
            // 
            this.txtMessageField.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtMessageField.Location = new Point(150, 112);
            this.txtMessageField.Name = "txtMessageField";
            this.txtMessageField.Size = new Size(380, 23);
            this.txtMessageField.TabIndex = 7;
            
            // 
            // lblErrorField
            // 
            this.lblErrorField.AutoSize = true;
            this.lblErrorField.Location = new Point(20, 145);
            this.lblErrorField.Name = "lblErrorField";
            this.lblErrorField.Size = new Size(65, 15);
            this.lblErrorField.TabIndex = 8;
            this.lblErrorField.Text = "Error Field:";
            
            // 
            // txtErrorField
            // 
            this.txtErrorField.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.txtErrorField.Location = new Point(150, 142);
            this.txtErrorField.Name = "txtErrorField";
            this.txtErrorField.Size = new Size(380, 23);
            this.txtErrorField.TabIndex = 9;
            
            // 
            // btnSave
            // 
            this.btnSave.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnSave.Location = new Point(416, 425);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(75, 30);
            this.btnSave.TabIndex = 1;
            this.btnSave.Text = "Save";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += this.btnSave_Click;
            
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnCancel.Location = new Point(497, 425);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(75, 30);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += this.btnCancel_Click;
            
            // 
            // ApiProviderForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(584, 467);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.tabControl);
            this.MinimumSize = new Size(600, 500);
            this.Name = "ApiProviderForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "API Provider";
            this.tabControl.ResumeLayout(false);
            this.tabBasic.ResumeLayout(false);
            this.tabBasic.PerformLayout();
            this.tabAdvanced.ResumeLayout(false);
            this.tabAdvanced.PerformLayout();
            this.tabResponseMapping.ResumeLayout(false);
            this.tabResponseMapping.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeoutSeconds)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRetryAttempts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRetryDelaySeconds)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl;
        private TabPage tabBasic;
        private Label lblName;
        private TextBox txtName;
        private Label lblBaseUrl;
        private TextBox txtBaseUrl;
        private Label lblFeasibilityEndpoint;
        private TextBox txtFeasibilityEndpoint;
        private Label lblApiKey;
        private TextBox txtApiKey;
        private Label lblAuthHeader;
        private TextBox txtAuthHeader;
        private CheckBox chkIsEnabled;
        private CheckBox chkIsDefault;
        private TabPage tabAdvanced;
        private Label lblTimeoutSeconds;
        private NumericUpDown numTimeoutSeconds;
        private Label lblMaxRetryAttempts;
        private NumericUpDown numMaxRetryAttempts;
        private Label lblRetryDelaySeconds;
        private NumericUpDown numRetryDelaySeconds;
        private Label lblRequestMethod;
        private ComboBox cmbRequestMethod;
        private Label lblContentType;
        private TextBox txtContentType;
        private Label lblRequestBodyTemplate;
        private TextBox txtRequestBodyTemplate;
        private Button btnTestTemplate;
        private TabPage tabResponseMapping;
        private Label lblFeasibleField;
        private TextBox txtFeasibleField;
        private Label lblFnoField;
        private TextBox txtFnoField;
        private Label lblProviderField;
        private TextBox txtProviderField;
        private Label lblMessageField;
        private TextBox txtMessageField;
        private Label lblErrorField;
        private TextBox txtErrorField;
        private Button btnSave;
        private Button btnCancel;
    }
}
