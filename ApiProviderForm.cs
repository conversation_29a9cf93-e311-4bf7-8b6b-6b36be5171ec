using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker;

public partial class ApiProviderForm : Form
{
    public ApiProvider ApiProvider { get; private set; }

    public ApiProviderForm(ApiProvider? provider = null)
    {
        InitializeComponent();
        
        if (provider != null)
        {
            ApiProvider = provider;
            LoadProvider(provider);
            this.Text = $"Edit API Provider - {provider.Name}";
        }
        else
        {
            ApiProvider = new ApiProvider();
            this.Text = "Add New API Provider";
            LoadDefaults();
        }
    }

    private void LoadProvider(ApiProvider provider)
    {
        txtName.Text = provider.Name;
        txtBaseUrl.Text = provider.BaseUrl;
        txtFeasibilityEndpoint.Text = provider.FeasibilityEndpoint;
        txtApiKey.Text = provider.ApiKey ?? string.Empty;
        txtAuthHeader.Text = provider.AuthHeader ?? string.Empty;
        numTimeoutSeconds.Value = provider.TimeoutSeconds;
        numMaxRetryAttempts.Value = provider.MaxRetryAttempts;
        numRetryDelaySeconds.Value = provider.RetryDelaySeconds;
        chkIsEnabled.Checked = provider.IsEnabled;
        chkIsDefault.Checked = provider.IsDefault;
        
        cmbRequestMethod.Text = provider.RequestMethod;
        txtContentType.Text = provider.ContentType;
        txtRequestBodyTemplate.Text = provider.RequestBodyTemplate;
        
        txtFeasibleField.Text = provider.FeasibleField;
        txtFnoField.Text = provider.FnoField;
        txtProviderField.Text = provider.ProviderField;
        txtMessageField.Text = provider.MessageField;
        txtErrorField.Text = provider.ErrorField;
    }

    private void LoadDefaults()
    {
        txtBaseUrl.Text = "https://";
        txtFeasibilityEndpoint.Text = "/api/feasibility";
        numTimeoutSeconds.Value = 30;
        numMaxRetryAttempts.Value = 3;
        numRetryDelaySeconds.Value = 2;
        chkIsEnabled.Checked = true;
        chkIsDefault.Checked = false;
        
        cmbRequestMethod.Text = "POST";
        txtContentType.Text = "application/json";
        txtRequestBodyTemplate.Text = "{\"address\":\"{ADDRESS}\"}";
        
        txtFeasibleField.Text = "feasible";
        txtFnoField.Text = "fno";
        txtProviderField.Text = "provider";
        txtMessageField.Text = "message";
        txtErrorField.Text = "error";
    }

    private void btnSave_Click(object sender, EventArgs e)
    {
        if (!ValidateInput())
            return;

        ApiProvider.Name = txtName.Text.Trim();
        ApiProvider.BaseUrl = txtBaseUrl.Text.Trim();
        ApiProvider.FeasibilityEndpoint = txtFeasibilityEndpoint.Text.Trim();
        ApiProvider.ApiKey = string.IsNullOrWhiteSpace(txtApiKey.Text) ? null : txtApiKey.Text.Trim();
        ApiProvider.AuthHeader = string.IsNullOrWhiteSpace(txtAuthHeader.Text) ? null : txtAuthHeader.Text.Trim();
        ApiProvider.TimeoutSeconds = (int)numTimeoutSeconds.Value;
        ApiProvider.MaxRetryAttempts = (int)numMaxRetryAttempts.Value;
        ApiProvider.RetryDelaySeconds = (int)numRetryDelaySeconds.Value;
        ApiProvider.IsEnabled = chkIsEnabled.Checked;
        ApiProvider.IsDefault = chkIsDefault.Checked;
        
        ApiProvider.RequestMethod = cmbRequestMethod.Text;
        ApiProvider.ContentType = txtContentType.Text.Trim();
        ApiProvider.RequestBodyTemplate = txtRequestBodyTemplate.Text.Trim();
        
        ApiProvider.FeasibleField = txtFeasibleField.Text.Trim();
        ApiProvider.FnoField = txtFnoField.Text.Trim();
        ApiProvider.ProviderField = txtProviderField.Text.Trim();
        ApiProvider.MessageField = txtMessageField.Text.Trim();
        ApiProvider.ErrorField = txtErrorField.Text.Trim();

        this.DialogResult = DialogResult.OK;
        this.Close();
    }

    private void btnCancel_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Cancel;
        this.Close();
    }

    private bool ValidateInput()
    {
        if (string.IsNullOrWhiteSpace(txtName.Text))
        {
            MessageBox.Show("Please enter a provider name.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            txtName.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(txtBaseUrl.Text))
        {
            MessageBox.Show("Please enter a base URL.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            txtBaseUrl.Focus();
            return false;
        }

        if (!Uri.TryCreate(txtBaseUrl.Text.Trim(), UriKind.Absolute, out _))
        {
            MessageBox.Show("Please enter a valid base URL.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            txtBaseUrl.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(txtFeasibilityEndpoint.Text))
        {
            MessageBox.Show("Please enter a feasibility endpoint.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            txtFeasibilityEndpoint.Focus();
            return false;
        }

        return true;
    }

    private void btnTestTemplate_Click(object sender, EventArgs e)
    {
        var template = txtRequestBodyTemplate.Text;
        var testAddress = "123 Test Street, Test City, 1234";
        var result = template.Replace("{ADDRESS}", testAddress);
        
        MessageBox.Show($"Template Test:\n\nTemplate: {template}\n\nTest Address: {testAddress}\n\nResult: {result}", 
            "Template Test Result", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }
}
