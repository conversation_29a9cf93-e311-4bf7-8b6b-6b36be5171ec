using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker.Services;

public interface IConfigurationService
{
    Task<List<ApiProvider>> GetApiProvidersAsync();
    Task SaveApiProvidersAsync(List<ApiProvider> providers);
    Task<GoogleApiSettings> GetGoogleApiSettingsAsync();
    Task SaveGoogleApiSettingsAsync(GoogleApiSettings settings);
    Task<AppSettings> GetAppSettingsAsync();
    Task SaveAppSettingsAsync(AppSettings settings);
    Task ReloadConfigurationAsync();
}
