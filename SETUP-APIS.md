# API Setup Guide - 28East & Google Places

## 🎯 **Quick Setup Instructions**

### **Step 1: Configure Google Places API**
1. **Open Configuration**: File → Configuration
2. **Go to Google Places API tab**
3. **Enter API Key**: `AIzaSyDEzt60rONb-C10rzDYhmK69IB2i16hALs`
4. **Check "Enabled"**
5. **Verify Settings**:
   - Base URL: `https://maps.googleapis.com/maps/api/place`
   - Endpoint: `/autocomplete/json`
   - Country: `za` (South Africa)
   - Place Types: `address`

### **Step 2: Configure 28East Octopi API**
1. **Go to API Providers tab**
2. **Click "Add Provider"**
3. **Configure Basic Settings**:
   - **Name**: `28East Octopi Portal`
   - **Base URL**: `https://octopi.28east.co.za`
   - **Feasibility Endpoint**: `/api/feasibility` (or correct endpoint)
   - **Enable**: ✅ Checked
   - **Set as Default**: ✅ Checked

4. **Configure Advanced Settings**:
   - **Request Method**: `POST`
   - **Content Type**: `application/json`
   - **Timeout**: `30` seconds
   - **Max Retry**: `3` attempts
   - **Request Body**: `{"address":"{ADDRESS}"}`

5. **Configure Response Mapping**:
   - **Feasible Field**: `feasible`
   - **FNO Field**: `fno`
   - **Provider Field**: `provider`
   - **Message Field**: `message`
   - **Error Field**: `error`

### **Step 3: Test Both APIs**

**Test Google Places**:
1. **Type in address field**: Start typing "123 Main Street, Cape Town"
2. **See autocomplete**: Should show suggestions with [Google] tags
3. **Verify functionality**: Suggestions should appear after 3+ characters

**Test 28East API**:
1. **Select 28East provider** in API Providers list
2. **Click "Test Provider"**
3. **Review results**: Will show success/failure with details
4. **Check response**: Look for proper JSON structure

### **Step 4: Go Live**
1. **Disable Demo Mode**: Configuration → Application Settings → Uncheck "Use Demo"
2. **Save Configuration**: Click Save and restart when prompted
3. **Test Real Queries**: Try actual addresses for feasibility checking

## 🔧 **Expected Test Results**

### **Google Places API Test**
- **✅ Success**: Autocomplete suggestions appear with [Google] tags
- **Response Time**: Should be < 2 seconds
- **Coverage**: South African addresses prioritized

### **28East API Test**
- **✅ Success**: API responds with JSON feasibility data
- **❌ Possible Issues**:
  - Authentication required (API key/token)
  - Different endpoint path
  - Different request format
  - CORS restrictions

## 🚨 **Troubleshooting**

### **Google Places Issues**
- **No suggestions**: Check API key is valid and Places API is enabled
- **Billing error**: Ensure billing is set up in Google Cloud Console
- **Wrong country**: Verify country restriction is set to "za"

### **28East API Issues**
- **Connection failed**: Check if octopi.28east.co.za is accessible
- **404 Not Found**: Try different endpoint paths:
  - `/api/v1/feasibility`
  - `/feasibility`
  - `/check-feasibility`
- **401 Unauthorized**: May need API key or authentication
- **CORS error**: API might not allow browser requests

## 📋 **Common 28East Endpoint Variations**

Try these if `/api/feasibility` doesn't work:
```
/api/v1/feasibility
/api/v2/feasibility
/feasibility
/check-feasibility
/fno-check
/coverage-check
```

## 🔍 **API Discovery Steps**

If 28East API test fails:
1. **Check website**: Visit https://octopi.28east.co.za manually
2. **Look for API docs**: Check for developer documentation
3. **Contact 28East**: Ask for API endpoint and authentication details
4. **Try different methods**: GET vs POST requests
5. **Check headers**: May need specific headers or tokens

## ✅ **Success Indicators**

**Google Places Working**:
- Address autocomplete shows suggestions
- Suggestions have [Google] provider tags
- Fast response times (< 2 seconds)

**28East API Working**:
- Test Provider shows ✅ SUCCESS
- Response contains feasibility data
- JSON structure matches expected fields

**Ready for Production**:
- Both APIs tested successfully
- Demo mode disabled
- Real feasibility checks working
- Address autocomplete functional

## 📞 **Next Steps**

1. **Test the setup** with the provided API key
2. **Report results** - which tests pass/fail
3. **Adjust configuration** based on test results
4. **Go live** once both APIs are working

Let's test these configurations and see what works! 🚀
