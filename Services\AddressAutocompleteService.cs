using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker.Services;

public class AddressAutocompleteService : IAddressAutocompleteService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AddressAutocompleteService> _logger;
    private readonly IConfigurationService _configurationService;

    public AddressAutocompleteService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<AddressAutocompleteService> logger,
        IConfigurationService configurationService)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _configurationService = configurationService;
    }

    public bool IsConfigured
    {
        get
        {
            try
            {
                var settings = _configurationService.GetAutocompleteSettingsAsync().Result;
                return (settings.Google.IsEnabled && !string.IsNullOrWhiteSpace(settings.Google.ApiKey) && settings.Google.ApiKey != "YOUR_GOOGLE_PLACES_API_KEY_HERE") ||
                       (settings.Radar.IsEnabled && !string.IsNullOrWhiteSpace(settings.Radar.ApiKey) && settings.Radar.ApiKey != "YOUR_RADAR_API_KEY_HERE");
            }
            catch
            {
                return false;
            }
        }
    }

    public AutocompleteProvider GetActiveProvider()
    {
        try
        {
            var settings = _configurationService.GetAutocompleteSettingsAsync().Result;

            if (settings.PreferredProvider == AutocompleteProvider.Both)
            {
                // If both are enabled, return the first available one
                if (settings.Google.IsEnabled && !string.IsNullOrWhiteSpace(settings.Google.ApiKey))
                    return AutocompleteProvider.Google;
                if (settings.Radar.IsEnabled && !string.IsNullOrWhiteSpace(settings.Radar.ApiKey))
                    return AutocompleteProvider.Radar;
            }
            else if (settings.PreferredProvider == AutocompleteProvider.Google && settings.Google.IsEnabled)
            {
                return AutocompleteProvider.Google;
            }
            else if (settings.PreferredProvider == AutocompleteProvider.Radar && settings.Radar.IsEnabled)
            {
                return AutocompleteProvider.Radar;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining active autocomplete provider");
        }

        return AutocompleteProvider.None;
    }

    public async Task<List<AddressSuggestion>> GetAddressSuggestionsAsync(string input, CancellationToken cancellationToken = default)
    {
        if (!IsConfigured)
        {
            _logger.LogWarning("No autocomplete providers are configured");
            return new List<AddressSuggestion>();
        }

        if (string.IsNullOrWhiteSpace(input))
        {
            return new List<AddressSuggestion>();
        }

        try
        {
            var settings = await _configurationService.GetAutocompleteSettingsAsync();

            if (input.Length < settings.MinCharacters)
            {
                return new List<AddressSuggestion>();
            }

            var suggestions = new List<AddressSuggestion>();

            // Get suggestions based on preferred provider
            switch (settings.PreferredProvider)
            {
                case AutocompleteProvider.Google:
                    if (settings.Google.IsEnabled)
                    {
                        suggestions.AddRange(await GetGoogleSuggestionsAsync(input, cancellationToken));

                        // Use Radar as fallback if enabled and Google failed
                        if (suggestions.Count == 0 && settings.UseFallback && settings.Radar.IsEnabled)
                        {
                            suggestions.AddRange(await GetRadarSuggestionsAsync(input, cancellationToken));
                        }
                    }
                    break;

                case AutocompleteProvider.Radar:
                    if (settings.Radar.IsEnabled)
                    {
                        suggestions.AddRange(await GetRadarSuggestionsAsync(input, cancellationToken));

                        // Use Google as fallback if enabled and Radar failed
                        if (suggestions.Count == 0 && settings.UseFallback && settings.Google.IsEnabled)
                        {
                            suggestions.AddRange(await GetGoogleSuggestionsAsync(input, cancellationToken));
                        }
                    }
                    break;

                case AutocompleteProvider.Both:
                    // Get from both providers and merge results
                    var tasks = new List<Task<List<AddressSuggestion>>>();

                    if (settings.Google.IsEnabled)
                        tasks.Add(GetGoogleSuggestionsAsync(input, cancellationToken));

                    if (settings.Radar.IsEnabled)
                        tasks.Add(GetRadarSuggestionsAsync(input, cancellationToken));

                    var results = await Task.WhenAll(tasks);

                    // Merge and deduplicate results
                    foreach (var result in results)
                    {
                        suggestions.AddRange(result);
                    }

                    // Remove duplicates based on description similarity
                    suggestions = DeduplicateSuggestions(suggestions);
                    break;
            }

            _logger.LogDebug("Retrieved {Count} address suggestions for input: {Input}", suggestions.Count, input);
            return suggestions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting address suggestions for input: {Input}", input);
            return new List<AddressSuggestion>();
        }
    }

    public async Task<List<AddressSuggestion>> GetGoogleSuggestionsAsync(string input, CancellationToken cancellationToken = default)
    {
        try
        {
            var settings = await _configurationService.GetAutocompleteSettingsAsync();

            if (!settings.Google.IsEnabled || string.IsNullOrWhiteSpace(settings.Google.ApiKey))
            {
                return new List<AddressSuggestion>();
            }

            var url = $"{settings.Google.BaseUrl}{settings.Google.AutocompleteEndpoint}";
            var queryParams = new Dictionary<string, string>
            {
                ["input"] = input,
                ["key"] = settings.Google.ApiKey,
                ["types"] = settings.Google.PlaceTypes,
                ["components"] = $"country:{settings.Google.CountryRestriction.ToLower()}"
            };

            var queryString = string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));
            var fullUrl = $"{url}?{queryString}";

            _logger.LogDebug("Requesting Google address suggestions for input: {Input}", input);

            var response = await _httpClient.GetAsync(fullUrl, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var placesResponse = JsonConvert.DeserializeObject<GooglePlacesResponse>(responseContent);

                if (placesResponse?.Status == "OK")
                {
                    _logger.LogDebug("Retrieved {Count} Google address suggestions", placesResponse.Predictions.Count);
                    return placesResponse.Predictions.Select(AddressSuggestion.FromGoogle).ToList();
                }
                else
                {
                    _logger.LogWarning("Google Places API returned status: {Status}", placesResponse?.Status);
                }
            }
            else
            {
                _logger.LogError("Google Places API request failed with status {StatusCode}: {Content}",
                    response.StatusCode, responseContent);
            }
        }
        catch (TaskCanceledException)
        {
            _logger.LogWarning("Google address autocomplete request was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Google address suggestions for input: {Input}", input);
        }

        return new List<AddressSuggestion>();
    }

    public async Task<List<AddressSuggestion>> GetRadarSuggestionsAsync(string input, CancellationToken cancellationToken = default)
    {
        try
        {
            var settings = await _configurationService.GetAutocompleteSettingsAsync();

            if (!settings.Radar.IsEnabled || string.IsNullOrWhiteSpace(settings.Radar.ApiKey))
            {
                return new List<AddressSuggestion>();
            }

            var url = $"{settings.Radar.BaseUrl}{settings.Radar.AutocompleteEndpoint}";
            var queryParams = new Dictionary<string, string>
            {
                ["query"] = input,
                ["country"] = settings.Radar.CountryRestriction,
                ["limit"] = settings.Radar.MaxResults.ToString()
            };

            var queryString = string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));
            var fullUrl = $"{url}?{queryString}";

            _logger.LogDebug("Requesting Radar address suggestions for input: {Input}", input);

            var request = new HttpRequestMessage(HttpMethod.Get, fullUrl);
            request.Headers.Add("Authorization", settings.Radar.ApiKey);

            var response = await _httpClient.SendAsync(request, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var radarResponse = JsonConvert.DeserializeObject<RadarAutocompleteResponse>(responseContent);

                if (radarResponse?.Addresses != null)
                {
                    _logger.LogDebug("Retrieved {Count} Radar address suggestions", radarResponse.Addresses.Count);
                    return radarResponse.Addresses.Select(AddressSuggestion.FromRadar).ToList();
                }
            }
            else
            {
                _logger.LogError("Radar API request failed with status {StatusCode}: {Content}",
                    response.StatusCode, responseContent);
            }
        }
        catch (TaskCanceledException)
        {
            _logger.LogWarning("Radar address autocomplete request was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Radar address suggestions for input: {Input}", input);
        }

        return new List<AddressSuggestion>();
    }

    private List<AddressSuggestion> DeduplicateSuggestions(List<AddressSuggestion> suggestions)
    {
        var deduplicated = new List<AddressSuggestion>();
        var seen = new HashSet<string>();

        foreach (var suggestion in suggestions)
        {
            // Create a normalized key for comparison
            var normalizedKey = suggestion.Description.ToLowerInvariant()
                .Replace(",", "")
                .Replace("  ", " ")
                .Trim();

            if (!seen.Contains(normalizedKey))
            {
                seen.Add(normalizedKey);
                deduplicated.Add(suggestion);
            }
        }

        return deduplicated.OrderBy(s => s.Description).ToList();
    }
}
