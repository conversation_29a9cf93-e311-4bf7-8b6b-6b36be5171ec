using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker.Services;

public class AddressAutocompleteService : IAddressAutocompleteService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AddressAutocompleteService> _logger;
    private readonly string? _apiKey;
    private readonly string _baseUrl;
    private readonly string _autocompleteEndpoint;

    public AddressAutocompleteService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<AddressAutocompleteService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;

        _apiKey = _configuration["GooglePlacesApi:ApiKey"];
        _baseUrl = _configuration["GooglePlacesApi:BaseUrl"] ?? "https://maps.googleapis.com/maps/api/place";
        _autocompleteEndpoint = _configuration["GooglePlacesApi:AutocompleteEndpoint"] ?? "/autocomplete/json";
    }

    public bool IsConfigured => !string.IsNullOrWhiteSpace(_apiKey) && _apiKey != "YOUR_GOOGLE_PLACES_API_KEY_HERE";

    public async Task<List<GooglePlacesPrediction>> GetAddressSuggestionsAsync(string input, CancellationToken cancellationToken = default)
    {
        if (!IsConfigured)
        {
            _logger.LogWarning("Google Places API is not configured. Please set a valid API key in appsettings.json");
            return new List<GooglePlacesPrediction>();
        }

        if (string.IsNullOrWhiteSpace(input) || input.Length < 3)
        {
            return new List<GooglePlacesPrediction>();
        }

        try
        {
            var url = $"{_baseUrl}{_autocompleteEndpoint}";
            var queryParams = new Dictionary<string, string>
            {
                ["input"] = input,
                ["key"] = _apiKey!,
                ["types"] = "address",
                ["components"] = "country:za" // Restrict to South Africa
            };

            var queryString = string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));
            var fullUrl = $"{url}?{queryString}";

            _logger.LogDebug("Requesting address suggestions for input: {Input}", input);

            var response = await _httpClient.GetAsync(fullUrl, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var placesResponse = JsonConvert.DeserializeObject<GooglePlacesResponse>(responseContent);
                
                if (placesResponse?.Status == "OK")
                {
                    _logger.LogDebug("Retrieved {Count} address suggestions", placesResponse.Predictions.Count);
                    return placesResponse.Predictions;
                }
                else
                {
                    _logger.LogWarning("Google Places API returned status: {Status}", placesResponse?.Status);
                }
            }
            else
            {
                _logger.LogError("Google Places API request failed with status {StatusCode}: {Content}", 
                    response.StatusCode, responseContent);
            }
        }
        catch (TaskCanceledException)
        {
            _logger.LogWarning("Address autocomplete request was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting address suggestions for input: {Input}", input);
        }

        return new List<GooglePlacesPrediction>();
    }
}
