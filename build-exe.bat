@echo off
echo Building Octopi Feasibility Checker as standalone executable...
echo This will create a single .exe file that doesn't require .NET to be installed.
echo.

dotnet publish OctopiFeasibilityChecker.csproj --configuration Release --output ./publish

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo.
    echo Executable created: .\publish\OctopiFeasibilityChecker.exe
    echo File size: 
    dir ".\publish\OctopiFeasibilityChecker.exe" | findstr "OctopiFeasibilityChecker.exe"
    echo.
    echo You can now run the application by double-clicking:
    echo .\publish\OctopiFeasibilityChecker.exe
    echo.
    echo Or copy the entire 'publish' folder to another computer and run it there.
    echo.
    pause
) else (
    echo.
    echo ❌ Build failed! Please check the errors above.
    echo.
    pause
)
