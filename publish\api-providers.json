[{"Id": "60d17d8d-3b26-4c33-934e-5ea441a85644", "Name": "28East Octopi Portal", "BaseUrl": "https://octopi.28east.co.za", "FeasibilityEndpoint": "/api/feasibility", "ApiKey": null, "AuthHeader": null, "TimeoutSeconds": 30, "MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "IsEnabled": true, "IsDefault": true, "CreatedAt": "2025-07-02T06:34:00.3631037+02:00", "LastTestedAt": null, "LastTestSuccessful": null, "LastTestError": null, "RequestMethod": "POST", "ContentType": "application/json", "RequestBodyTemplate": "{\"address\":\"{ADDRESS}\"}", "FeasibleField": "feasible", "FnoField": "fno", "ProviderField": "provider", "MessageField": "message", "ErrorField": "error"}, {"Id": "85bb98b1-23b4-48a0-a1b8-1a6b63d2c04a", "Name": "Radar", "BaseUrl": "https://api.radar.io/v1/search/autocomplete", "FeasibilityEndpoint": "/api/feasibility", "ApiKey": "prj_live_sk_0d13688d111274363fb1df0406b78fdcf0cc57f3", "AuthHeader": null, "TimeoutSeconds": 30, "MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "IsEnabled": true, "IsDefault": false, "CreatedAt": "2025-07-02T06:42:00.7701413+02:00", "LastTestedAt": null, "LastTestSuccessful": null, "LastTestError": null, "RequestMethod": "POST", "ContentType": "application/json", "RequestBodyTemplate": "{\"address\":\"{ADDRESS}\"}", "FeasibleField": "feasible", "FnoField": "fno", "ProviderField": "provider", "MessageField": "message", "ErrorField": "error"}]