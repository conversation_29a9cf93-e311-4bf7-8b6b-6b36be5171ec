using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker.Services;

public interface IAddressAutocompleteService
{
    Task<List<AddressSuggestion>> GetAddressSuggestionsAsync(string input, CancellationToken cancellationToken = default);
    bool IsConfigured { get; }
    AutocompleteProvider GetActiveProvider();
    Task<List<AddressSuggestion>> GetGoogleSuggestionsAsync(string input, CancellationToken cancellationToken = default);
    Task<List<AddressSuggestion>> GetRadarSuggestionsAsync(string input, CancellationToken cancellationToken = default);
}
