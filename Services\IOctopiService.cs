using OctopiFeasibilityChecker.Models;

namespace OctopiFeasibilityChecker.Services;

public interface IOctopiService
{
    Task<FeasibilityResult> CheckFeasibilityAsync(string address, CancellationToken cancellationToken = default);
    Task<List<FeasibilityResult>> CheckMultipleFeasibilityAsync(IEnumerable<string> addresses, IProgress<int>? progress = null, CancellationToken cancellationToken = default);
}
