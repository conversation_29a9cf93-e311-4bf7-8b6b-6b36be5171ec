# Octopi Feasibility Checker

A Windows Forms (.NET 6) application for checking fibre feasibility using the Octopi portal (https://octopi.28east.co.za).

## Features

- **Single Address Check**: Enter an address and check fibre feasibility instantly
- **Batch Processing**: Load addresses from .txt or .csv files and process them in bulk
- **Address Autocomplete**: Google Places API integration for address suggestions (optional)
- **Caching**: Intelligent caching to avoid duplicate API calls
- **CSV Export**: Export results to CSV files with detailed information
- **Retry Logic**: Automatic retry with exponential backoff for failed requests
- **Progress Tracking**: Real-time progress updates for batch operations
- **Error Handling**: Comprehensive error handling with detailed logging

## Requirements

- .NET 6.0 Runtime
- Windows 10/11
- Internet connection for API calls

## Setup Instructions

### 1. Clone or Download the Project

```bash
git clone <repository-url>
cd OctopiFeasibilityChecker
```

### 2. Configure API Settings

Edit the `appsettings.json` file to configure the application:

```json
{
  "OctopiApi": {
    "BaseUrl": "https://octopi.28east.co.za",
    "FeasibilityEndpoint": "/api/feasibility",
    "TimeoutSeconds": 30,
    "MaxRetryAttempts": 3,
    "RetryDelaySeconds": 2
  },
  "GooglePlacesApi": {
    "ApiKey": "YOUR_GOOGLE_PLACES_API_KEY_HERE",
    "BaseUrl": "https://maps.googleapis.com/maps/api/place",
    "AutocompleteEndpoint": "/autocomplete/json"
  }
}
```

### 3. Google Places API Setup (Optional)

To enable address autocomplete:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Places API
4. Create an API key
5. Replace `YOUR_GOOGLE_PLACES_API_KEY_HERE` in `appsettings.json` with your actual API key

**Note**: The application works without Google Places API, but autocomplete will be disabled.

### 4. Build and Run

```bash
dotnet build
dotnet run
```

Or open the solution in Visual Studio and run from there.

## Usage

### Single Address Check

1. Enter an address in the text box (e.g., "6 Russell St, Mbombela, 1201")
2. Click "Check FNO" or press Enter
3. View the result in the result text box

### Batch Processing

1. Prepare a file with addresses:
   - **Text file (.txt)**: One address per line
   - **CSV file (.csv)**: Addresses in the first column or a column named "address"
2. Click "Load Address File"
3. Select your file
4. Confirm batch processing
5. Monitor progress and view results

### Export Results

1. After checking addresses, click "Export CSV"
2. Choose a location to save the file
3. The CSV will contain all results with timestamps and cache information

## File Formats

### Input Files

**Text File Example (addresses.txt):**
```
6 Russell St, Mbombela, 1201
123 Main Road, Cape Town, 8001
456 Oak Avenue, Johannesburg, 2000
```

**CSV File Example (addresses.csv):**
```csv
address,notes
6 Russell St, Mbombela, 1201,Primary location
123 Main Road, Cape Town, 8001,Branch office
456 Oak Avenue, Johannesburg, 2000,Backup site
```

### Output CSV Format

The exported CSV contains the following columns:
- Address
- Feasible (True/False)
- FNO (Fibre Network Operator)
- Provider
- Message
- Error
- CheckedAt (Timestamp)
- FromCache (True/False)

## Configuration Options

### Cache Settings

```json
"Cache": {
  "ExpirationMinutes": 60,
  "MaxItems": 1000
}
```

### Export Settings

```json
"Export": {
  "DefaultDirectory": "exports",
  "DateFormat": "yyyy-MM-dd_HH-mm-ss"
}
```

### Logging

Logs are automatically saved to the `logs/` directory with daily rotation.

## Troubleshooting

### Common Issues

1. **API Timeout**: Increase `TimeoutSeconds` in configuration
2. **Rate Limiting**: Increase `RetryDelaySeconds` or reduce batch size
3. **Address Not Found**: Ensure addresses are properly formatted
4. **Google Places Not Working**: Verify API key and billing is enabled

### Error Messages

- **"Address cannot be empty"**: Enter a valid address
- **"API returned 4xx/5xx"**: Check Octopi API status
- **"Request timeout"**: Check internet connection or increase timeout
- **"File not found"**: Verify file path and permissions

## API Response Format

The Octopi API returns JSON in this format:

```json
{
  "feasible": true,
  "fno": "Openserve",
  "provider": "Telkom",
  "message": "Fibre available",
  "error": null
}
```

## Development

### Project Structure

```
OctopiFeasibilityChecker/
├── Models/                 # Data models
├── Services/              # Business logic services
├── MainForm.cs           # Main UI form
├── Program.cs            # Application entry point
├── appsettings.json      # Configuration
└── README.md            # This file
```

### Dependencies

- Newtonsoft.Json - JSON serialization
- CsvHelper - CSV file handling
- Microsoft.Extensions.* - Dependency injection, logging, caching
- Serilog - Structured logging

## License

This project is licensed under the MIT License.

## Support

For issues or questions, please check the logs in the `logs/` directory for detailed error information.
