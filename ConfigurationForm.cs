using Microsoft.Extensions.Logging;
using OctopiFeasibilityChecker.Models;
using OctopiFeasibilityChecker.Services;

namespace OctopiFeasibilityChecker;

public partial class ConfigurationForm : Form
{
    private readonly IConfigurationService _configurationService;
    private readonly ILogger<ConfigurationForm> _logger;
    private List<ApiProvider> _apiProviders = new();
    private GoogleApiSettings _googleApiSettings = new();
    private AppSettings _appSettings = new();

    public ConfigurationForm(IConfigurationService configurationService, ILogger<ConfigurationForm> logger)
    {
        _configurationService = configurationService;
        _logger = logger;
        InitializeComponent();
        LoadConfiguration();
    }

    private async void LoadConfiguration()
    {
        try
        {
            _apiProviders = await _configurationService.GetApiProvidersAsync();
            _googleApiSettings = await _configurationService.GetGoogleApiSettingsAsync();
            _appSettings = await _configurationService.GetAppSettingsAsync();

            RefreshApiProvidersList();
            LoadGoogleApiSettings();
            LoadAppSettings();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading configuration");
            MessageBox.Show($"Error loading configuration: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void RefreshApiProvidersList()
    {
        lstApiProviders.Items.Clear();
        foreach (var provider in _apiProviders)
        {
            var item = new ListViewItem(provider.Name);
            item.SubItems.Add(provider.BaseUrl);
            item.SubItems.Add(provider.IsEnabled ? "Yes" : "No");
            item.SubItems.Add(provider.IsDefault ? "Yes" : "No");
            item.SubItems.Add(provider.LastTestedAt?.ToString("yyyy-MM-dd HH:mm") ?? "Never");
            item.Tag = provider;
            
            if (!provider.IsEnabled)
                item.ForeColor = Color.Gray;
            else if (provider.IsDefault)
                item.Font = new Font(item.Font, FontStyle.Bold);

            lstApiProviders.Items.Add(item);
        }
    }

    private void LoadGoogleApiSettings()
    {
        txtGoogleApiKey.Text = _googleApiSettings.ApiKey;
        chkGoogleApiEnabled.Checked = _googleApiSettings.IsEnabled;
        txtGoogleBaseUrl.Text = _googleApiSettings.BaseUrl;
        txtGoogleEndpoint.Text = _googleApiSettings.AutocompleteEndpoint;
        txtCountryRestriction.Text = _googleApiSettings.CountryRestriction;
        txtPlaceTypes.Text = _googleApiSettings.PlaceTypes;
    }

    private void LoadAppSettings()
    {
        chkUseDemo.Checked = _appSettings.UseDemo;
        numCacheExpiration.Value = _appSettings.CacheExpirationMinutes;
        numMaxCacheItems.Value = _appSettings.MaxCacheItems;
        txtExportDirectory.Text = _appSettings.ExportDirectory;
        txtDateFormat.Text = _appSettings.DateFormat;
    }

    private async void btnSave_Click(object sender, EventArgs e)
    {
        try
        {
            // Update Google API settings
            _googleApiSettings.ApiKey = txtGoogleApiKey.Text.Trim();
            _googleApiSettings.IsEnabled = chkGoogleApiEnabled.Checked && !string.IsNullOrWhiteSpace(_googleApiSettings.ApiKey);
            _googleApiSettings.BaseUrl = txtGoogleBaseUrl.Text.Trim();
            _googleApiSettings.AutocompleteEndpoint = txtGoogleEndpoint.Text.Trim();
            _googleApiSettings.CountryRestriction = txtCountryRestriction.Text.Trim();
            _googleApiSettings.PlaceTypes = txtPlaceTypes.Text.Trim();

            // Update app settings
            _appSettings.UseDemo = chkUseDemo.Checked;
            _appSettings.CacheExpirationMinutes = (int)numCacheExpiration.Value;
            _appSettings.MaxCacheItems = (int)numMaxCacheItems.Value;
            _appSettings.ExportDirectory = txtExportDirectory.Text.Trim();
            _appSettings.DateFormat = txtDateFormat.Text.Trim();

            // Save all settings
            await _configurationService.SaveApiProvidersAsync(_apiProviders);
            await _configurationService.SaveGoogleApiSettingsAsync(_googleApiSettings);
            await _configurationService.SaveAppSettingsAsync(_appSettings);
            await _configurationService.ReloadConfigurationAsync();

            MessageBox.Show("Configuration saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving configuration");
            MessageBox.Show($"Error saving configuration: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void btnCancel_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Cancel;
        this.Close();
    }

    private void btnAddProvider_Click(object sender, EventArgs e)
    {
        using var form = new ApiProviderForm();
        if (form.ShowDialog() == DialogResult.OK)
        {
            _apiProviders.Add(form.ApiProvider);
            RefreshApiProvidersList();
        }
    }

    private void btnEditProvider_Click(object sender, EventArgs e)
    {
        if (lstApiProviders.SelectedItems.Count == 0)
        {
            MessageBox.Show("Please select an API provider to edit.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var selectedProvider = (ApiProvider)lstApiProviders.SelectedItems[0].Tag;
        using var form = new ApiProviderForm(selectedProvider);
        if (form.ShowDialog() == DialogResult.OK)
        {
            var index = _apiProviders.FindIndex(p => p.Id == selectedProvider.Id);
            if (index >= 0)
            {
                _apiProviders[index] = form.ApiProvider;
                RefreshApiProvidersList();
            }
        }
    }

    private void btnDeleteProvider_Click(object sender, EventArgs e)
    {
        if (lstApiProviders.SelectedItems.Count == 0)
        {
            MessageBox.Show("Please select an API provider to delete.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var selectedProvider = (ApiProvider)lstApiProviders.SelectedItems[0].Tag;
        var result = MessageBox.Show($"Are you sure you want to delete the API provider '{selectedProvider.Name}'?", 
            "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            _apiProviders.RemoveAll(p => p.Id == selectedProvider.Id);
            RefreshApiProvidersList();
        }
    }

    private void btnTestProvider_Click(object sender, EventArgs e)
    {
        if (lstApiProviders.SelectedItems.Count == 0)
        {
            MessageBox.Show("Please select an API provider to test.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var selectedProvider = (ApiProvider)lstApiProviders.SelectedItems[0].Tag;

        // TODO: Implement API testing logic
        MessageBox.Show($"Testing API provider '{selectedProvider.Name}'...\n\nThis feature will be implemented to test the actual API endpoint.",
            "API Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void btnSetDefault_Click(object sender, EventArgs e)
    {
        if (lstApiProviders.SelectedItems.Count == 0)
        {
            MessageBox.Show("Please select an API provider to set as default.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var selectedProvider = (ApiProvider)lstApiProviders.SelectedItems[0].Tag;
        
        // Clear all default flags
        foreach (var provider in _apiProviders)
        {
            provider.IsDefault = false;
        }
        
        // Set selected as default
        selectedProvider.IsDefault = true;
        _appSettings.DefaultApiProviderId = selectedProvider.Id;
        
        RefreshApiProvidersList();
    }

    private void chkGoogleApiEnabled_CheckedChanged(object sender, EventArgs e)
    {
        var enabled = chkGoogleApiEnabled.Checked && !string.IsNullOrWhiteSpace(txtGoogleApiKey.Text);
        txtGoogleBaseUrl.Enabled = enabled;
        txtGoogleEndpoint.Enabled = enabled;
        txtCountryRestriction.Enabled = enabled;
        txtPlaceTypes.Enabled = enabled;
    }

    private void txtGoogleApiKey_TextChanged(object sender, EventArgs e)
    {
        chkGoogleApiEnabled_CheckedChanged(sender, e);
    }

    private Icon? LoadIcon()
    {
        try
        {
            var iconPath = Path.Combine(Directory.GetCurrentDirectory(), "OCTOPI.png");
            if (File.Exists(iconPath))
            {
                using var bitmap = new Bitmap(iconPath);
                return Icon.FromHandle(bitmap.GetHicon());
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not load application icon");
        }
        return null;
    }
}
