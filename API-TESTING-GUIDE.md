# API Testing Guide

## 🧪 **API Provider Testing Feature**

The application now includes **comprehensive API testing functionality** that allows you to test your API providers and see detailed results.

## 🚀 **How to Test API Providers**

### **Step 1: Add an API Provider**
1. **Open Configuration**: File → Configuration
2. **Go to API Providers tab**
3. **Click "Add Provider"**
4. **Configure your provider**:
   - Name: e.g., "28East Octopi"
   - Base URL: e.g., "https://octopi.28east.co.za"
   - Feasibility Endpoint: e.g., "/api/feasibility"
   - Request Method: POST
   - Content Type: application/json
   - Request Body Template: `{"address":"{ADDRESS}"}`

### **Step 2: Test the Provider**
1. **Select the provider** in the list
2. **Click "Test Provider"**
3. **Wait for the test** (button shows "Testing...")
4. **View detailed results** in the popup window

## 📊 **Test Results Explained**

### **Success Indicators**
- **✅ SUCCESS**: API responded correctly
- **Green background**: Test passed
- **Response time**: How fast the API responded
- **Status code**: HTTP response code (200 = OK)

### **Failure Indicators**
- **❌ FAILED**: API test failed
- **Red background**: Test failed
- **Error details**: Specific reason for failure
- **Response content**: What the API returned

## 🔍 **What the Test Does**

### **Test Process**
1. **Sends test request** with address: "123 Test Street, Test City, 1234"
2. **Measures response time**
3. **Checks HTTP status code**
4. **Validates JSON response structure**
5. **Verifies expected fields exist** (feasible, fno, provider)
6. **Updates provider status** (Last Tested, Success/Fail)

### **Test Validation**
- **URL Format**: Validates base URL and endpoint
- **Network Connectivity**: Tests if API is reachable
- **Response Format**: Checks if response is valid JSON
- **Field Mapping**: Verifies expected fields exist
- **Timeout Handling**: Respects configured timeout settings

## 📋 **Test Result Details**

### **Summary Tab**
- Complete test report
- All test parameters and results
- Formatted for easy reading

### **Error Details Tab**
- Specific error information
- Network errors, timeouts, etc.
- Troubleshooting information

### **Response Content Tab**
- Raw API response
- Formatted JSON (if valid)
- Helps debug API issues

## 🛠️ **Common Test Results**

### **✅ Successful Tests**
```
✅ SUCCESS (1250ms) - API responded successfully with valid JSON structure
Status Code: 200
Response Time: 1250ms
```

### **❌ Network Errors**
```
❌ FAILED - Network error occurred
Error: No such host is known (octopi.28east.co.za)
```

### **❌ Timeout Errors**
```
❌ FAILED - Request timed out
Error: Request exceeded 30 second timeout
```

### **❌ Invalid Response**
```
❌ FAILED - API responded but returned invalid JSON
Error: Unexpected character encountered while parsing
```

### **❌ Wrong Structure**
```
❌ FAILED - API responded but JSON structure doesn't match expected fields
Expected fields: feasible, fno, provider
```

## 🔧 **Troubleshooting Failed Tests**

### **Network Issues**
- **Check URL spelling**: Ensure base URL is correct
- **Verify connectivity**: Test URL in browser
- **Check firewall**: Ensure outbound connections allowed
- **DNS issues**: Try IP address instead of domain

### **Authentication Issues**
- **API Key required**: Check if API needs authentication
- **Wrong auth header**: Verify header name (Authorization, X-API-Key, etc.)
- **Invalid credentials**: Confirm API key is correct

### **Response Format Issues**
- **HTML instead of JSON**: API might return error page
- **Different field names**: Update response mapping
- **Missing fields**: Check API documentation

### **Timeout Issues**
- **Increase timeout**: Go to Advanced Settings tab
- **Slow API**: Some APIs take longer to respond
- **Network latency**: Consider geographic distance

## 📈 **Using Test Results**

### **Provider Status Updates**
- **Last Tested**: Shows when provider was last tested
- **Success/Fail**: Quick visual indicator in provider list
- **Error tracking**: Stores last error for troubleshooting

### **Configuration Adjustments**
Based on test results, you might need to:
- **Adjust timeouts** for slow APIs
- **Update field mappings** for different response formats
- **Change authentication** headers or methods
- **Modify request templates** for different API formats

## 🎯 **Best Practices**

### **Before Going Live**
1. **Test all providers** before disabling demo mode
2. **Verify response mapping** matches actual API responses
3. **Check timeout settings** are appropriate
4. **Test with different addresses** if possible

### **Regular Testing**
- **Test providers periodically** to ensure they're still working
- **Monitor response times** for performance issues
- **Check for API changes** that might break integration

### **Debugging Tips**
- **Copy test reports** for sharing with API providers
- **Check logs** in the logs/ folder for detailed information
- **Use browser developer tools** to compare with manual API calls
- **Test with curl or Postman** to isolate issues

## 📞 **Getting Help**

If API tests fail:

1. **Check the detailed error message**
2. **Copy the test report** for reference
3. **Verify API documentation** matches your configuration
4. **Contact the API provider** if needed
5. **Check application logs** for additional details

The API testing feature helps ensure your providers are working correctly before you rely on them for real feasibility checks! 🚀
