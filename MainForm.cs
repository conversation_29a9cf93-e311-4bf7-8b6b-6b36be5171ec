using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OctopiFeasibilityChecker.Models;
using OctopiFeasibilityChecker.Services;
using System.ComponentModel;

namespace OctopiFeasibilityChecker;

public partial class MainForm : Form
{
    private readonly IOctopiService _octopiService;
    private readonly ICsvExportService _csvExportService;
    private readonly IAddressAutocompleteService _addressAutocompleteService;
    private readonly ICacheService _cacheService;
    private readonly IConfiguration _configuration;
    private readonly IConfigurationService _configurationService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MainForm> _logger;

    private readonly List<FeasibilityResult> _results = new();
    private CancellationTokenSource? _cancellationTokenSource;
    private System.Windows.Forms.Timer? _autocompleteTimer;
    private List<GooglePlacesPrediction> _currentSuggestions = new();

    public MainForm(
        IOctopiService octopiService,
        ICsvExportService csvExportService,
        IAddressAutocompleteService addressAutocompleteService,
        ICacheService cacheService,
        IConfiguration configuration,
        IConfigurationService configurationService,
        IServiceProvider serviceProvider,
        ILogger<MainForm> logger)
    {
        _octopiService = octopiService;
        _csvExportService = csvExportService;
        _addressAutocompleteService = addressAutocompleteService;
        _cacheService = cacheService;
        _configuration = configuration;
        _configurationService = configurationService;
        _serviceProvider = serviceProvider;
        _logger = logger;

        InitializeComponent();
        InitializeAutocomplete();
        ShowDemoModeNotification();
        UpdateUI();
    }

    private void InitializeAutocomplete()
    {
        if (_addressAutocompleteService.IsConfigured)
        {
            _autocompleteTimer = new System.Windows.Forms.Timer();
            _autocompleteTimer.Interval = 500; // 500ms delay
            _autocompleteTimer.Tick += AutocompleteTimer_Tick;

            txtAddress.TextChanged += TxtAddress_TextChanged;
            txtAddress.KeyDown += TxtAddress_KeyDown;
        }
        else
        {
            _logger.LogWarning("Google Places API not configured. Address autocomplete will not be available.");
        }
    }

    private void TxtAddress_TextChanged(object? sender, EventArgs e)
    {
        _autocompleteTimer?.Stop();
        
        if (txtAddress.Text.Length >= 3)
        {
            _autocompleteTimer?.Start();
        }
        else
        {
            HideAutocompleteSuggestions();
        }
    }

    private async void AutocompleteTimer_Tick(object? sender, EventArgs e)
    {
        _autocompleteTimer?.Stop();
        
        try
        {
            var suggestions = await _addressAutocompleteService.GetAddressSuggestionsAsync(txtAddress.Text);
            _currentSuggestions = suggestions;
            ShowAutocompleteSuggestions(suggestions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting address suggestions");
        }
    }

    private void ShowAutocompleteSuggestions(List<GooglePlacesPrediction> suggestions)
    {
        // Hide existing suggestions
        HideAutocompleteSuggestions();

        if (suggestions.Count == 0) return;

        // Create and show suggestion list
        var suggestionList = new ListBox
        {
            Location = new Point(txtAddress.Left, txtAddress.Bottom),
            Width = txtAddress.Width,
            Height = Math.Min(suggestions.Count * 20 + 4, 120),
            Font = txtAddress.Font
        };

        foreach (var suggestion in suggestions)
        {
            suggestionList.Items.Add(suggestion.Description);
        }

        suggestionList.Click += (s, e) =>
        {
            if (suggestionList.SelectedIndex >= 0)
            {
                txtAddress.Text = suggestionList.SelectedItem?.ToString() ?? "";
                HideAutocompleteSuggestions();
            }
        };

        suggestionList.KeyDown += (s, e) =>
        {
            if (e.KeyCode == Keys.Enter && suggestionList.SelectedIndex >= 0)
            {
                txtAddress.Text = suggestionList.SelectedItem?.ToString() ?? "";
                HideAutocompleteSuggestions();
                txtAddress.Focus();
            }
            else if (e.KeyCode == Keys.Escape)
            {
                HideAutocompleteSuggestions();
                txtAddress.Focus();
            }
        };

        this.Controls.Add(suggestionList);
        suggestionList.BringToFront();
        suggestionList.Tag = "autocomplete";
    }

    private void HideAutocompleteSuggestions()
    {
        var controlsToRemove = this.Controls.Cast<Control>()
            .Where(c => c.Tag?.ToString() == "autocomplete")
            .ToList();

        foreach (var control in controlsToRemove)
        {
            this.Controls.Remove(control);
            control.Dispose();
        }
    }

    private void TxtAddress_KeyDown(object? sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.SuppressKeyPress = true;
            btnCheckFno_Click(this, e);
        }
        else if (e.KeyCode == Keys.Escape)
        {
            HideAutocompleteSuggestions();
        }
    }

    private async void btnCheckFno_Click(object sender, EventArgs e)
    {
        var address = txtAddress.Text.Trim();
        if (string.IsNullOrWhiteSpace(address))
        {
            MessageBox.Show("Please enter an address.", "Address Required", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        await CheckSingleAddressAsync(address);
    }

    private async Task CheckSingleAddressAsync(string address)
    {
        try
        {
            SetUIBusy(true);
            _cancellationTokenSource = new CancellationTokenSource();

            var result = await _octopiService.CheckFeasibilityAsync(address, _cancellationTokenSource.Token);
            
            _results.Add(result);
            DisplayResult(result);
            UpdateResultsList();
            UpdateUI();

            _logger.LogInformation("Feasibility check completed for address: {Address}", address);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Feasibility check was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking feasibility for address: {Address}", address);
            MessageBox.Show($"Error checking feasibility: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            SetUIBusy(false);
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }
    }

    private void DisplayResult(FeasibilityResult result)
    {
        var displayText = result.GetDisplayText();
        if (result.FromCache)
        {
            displayText += " (from cache)";
        }

        txtResult.Text = displayText;
        txtResult.ForeColor = result.Feasible ? Color.Green : 
                             !string.IsNullOrEmpty(result.Error) ? Color.Red : Color.Orange;
    }

    private void UpdateResultsList()
    {
        lstResults.Items.Clear();
        foreach (var result in _results.OrderByDescending(r => r.CheckedAt))
        {
            var item = new ListViewItem(result.Address);
            item.SubItems.Add(result.Feasible ? "Yes" : "No");
            item.SubItems.Add(result.Fno);
            item.SubItems.Add(result.Provider);
            item.SubItems.Add(result.CheckedAt.ToString("yyyy-MM-dd HH:mm:ss"));
            item.SubItems.Add(result.FromCache ? "Yes" : "No");
            item.Tag = result;

            if (!string.IsNullOrEmpty(result.Error))
            {
                item.ForeColor = Color.Red;
                item.SubItems.Add(result.Error);
            }
            else
            {
                item.ForeColor = result.Feasible ? Color.Green : Color.Orange;
                item.SubItems.Add(result.Message ?? "");
            }

            lstResults.Items.Add(item);
        }
    }

    private void UpdateUI()
    {
        btnExportCsv.Enabled = _results.Count > 0;
        btnClearResults.Enabled = _results.Count > 0;
        lblResultCount.Text = $"Results: {_results.Count}";
        lblCacheCount.Text = $"Cache: {_cacheService.GetCacheCount()}";
    }

    private void SetUIBusy(bool busy)
    {
        btnCheckFno.Enabled = !busy;
        btnLoadFile.Enabled = !busy;
        btnExportCsv.Enabled = !busy && _results.Count > 0;
        btnClearResults.Enabled = !busy && _results.Count > 0;
        btnClearCache.Enabled = !busy;

        if (busy)
        {
            this.Cursor = Cursors.WaitCursor;
            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.Visible = true;
        }
        else
        {
            this.Cursor = Cursors.Default;
            progressBar.Style = ProgressBarStyle.Continuous;
            progressBar.Visible = false;
        }
    }

    private async void btnLoadFile_Click(object sender, EventArgs e)
    {
        using var openFileDialog = new OpenFileDialog
        {
            Title = "Select Address File",
            Filter = "Text Files (*.txt)|*.txt|CSV Files (*.csv)|*.csv|All Files (*.*)|*.*",
            FilterIndex = 1
        };

        if (openFileDialog.ShowDialog() == DialogResult.OK)
        {
            await ProcessFileAsync(openFileDialog.FileName);
        }
    }

    private async Task ProcessFileAsync(string filePath)
    {
        try
        {
            SetUIBusy(true);
            _cancellationTokenSource = new CancellationTokenSource();

            var addresses = await _csvExportService.ImportAddressesAsync(filePath);

            if (addresses.Count == 0)
            {
                MessageBox.Show("No addresses found in the file.", "No Addresses", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show(
                $"Found {addresses.Count} addresses. Do you want to process them all?",
                "Confirm Batch Processing",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result != DialogResult.Yes) return;

            // Setup progress tracking
            progressBar.Style = ProgressBarStyle.Continuous;
            progressBar.Maximum = addresses.Count;
            progressBar.Value = 0;
            progressBar.Visible = true;

            var progress = new Progress<int>(value =>
            {
                if (InvokeRequired)
                {
                    Invoke(() =>
                    {
                        progressBar.Value = value;
                        lblStatus.Text = $"Processing {value}/{addresses.Count} addresses...";
                    });
                }
                else
                {
                    progressBar.Value = value;
                    lblStatus.Text = $"Processing {value}/{addresses.Count} addresses...";
                }
            });

            var results = await _octopiService.CheckMultipleFeasibilityAsync(addresses, progress, _cancellationTokenSource.Token);

            _results.AddRange(results);
            UpdateResultsList();
            UpdateUI();

            lblStatus.Text = $"Completed processing {results.Count} addresses.";

            var successCount = results.Count(r => string.IsNullOrEmpty(r.Error));
            var errorCount = results.Count - successCount;

            MessageBox.Show(
                $"Processing completed!\n\nSuccessful: {successCount}\nErrors: {errorCount}",
                "Batch Processing Complete",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);

            _logger.LogInformation("Batch processing completed. Successful: {SuccessCount}, Errors: {ErrorCount}", successCount, errorCount);
        }
        catch (OperationCanceledException)
        {
            lblStatus.Text = "Processing cancelled.";
            _logger.LogInformation("Batch processing was cancelled");
        }
        catch (Exception ex)
        {
            lblStatus.Text = "Error during processing.";
            _logger.LogError(ex, "Error during batch processing");
            MessageBox.Show($"Error processing file: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            SetUIBusy(false);
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }
    }

    private async void btnExportCsv_Click(object sender, EventArgs e)
    {
        if (_results.Count == 0)
        {
            MessageBox.Show("No results to export.", "No Results", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        try
        {
            using var saveFileDialog = new SaveFileDialog
            {
                Title = "Export Results to CSV",
                Filter = "CSV Files (*.csv)|*.csv",
                DefaultExt = "csv",
                FileName = _csvExportService.GetDefaultExportPath().Split('\\').Last()
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                var filePath = await _csvExportService.ExportResultsAsync(_results, saveFileDialog.FileName);

                var result = MessageBox.Show(
                    $"Results exported successfully to:\n{filePath}\n\nWould you like to open the file?",
                    "Export Complete",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Information);

                if (result == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting results to CSV");
            MessageBox.Show($"Error exporting results: {ex.Message}", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void btnClearResults_Click(object sender, EventArgs e)
    {
        var result = MessageBox.Show(
            "Are you sure you want to clear all results?",
            "Clear Results",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            _results.Clear();
            UpdateResultsList();
            UpdateUI();
            txtResult.Clear();
            lblStatus.Text = "Results cleared.";
        }
    }

    private async void btnClearCache_Click(object sender, EventArgs e)
    {
        var result = MessageBox.Show(
            "Are you sure you want to clear the cache?",
            "Clear Cache",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            await _cacheService.ClearAsync();
            UpdateUI();
            lblStatus.Text = "Cache cleared.";
        }
    }

    private void btnCancel_Click(object sender, EventArgs e)
    {
        _cancellationTokenSource?.Cancel();
        lblStatus.Text = "Cancelling...";
    }

    private void ShowDemoModeNotification()
    {
        var useDemo = bool.Parse(_configuration["OctopiApi:UseDemo"] ?? "true");
        if (useDemo)
        {
            var message = "🎯 DEMO MODE ACTIVE\n\n" +
                         "This application is running in demo mode with simulated data because:\n\n" +
                         "• The Octopi portal (octopi.28east.co.za) doesn't provide a public API\n" +
                         "• It's a web application that requires browser interaction\n" +
                         "• All endpoints return HTML instead of JSON data\n\n" +
                         "The demo provides realistic South African fibre feasibility responses.\n\n" +
                         "See API-DISCOVERY.md for details on finding real API endpoints.\n\n" +
                         "Click OK to continue with demo mode.";

            MessageBox.Show(message, "Demo Mode - Octopi Feasibility Checker",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    private void menuConfiguration_Click(object sender, EventArgs e)
    {
        try
        {
            var configForm = _serviceProvider.GetService(typeof(ConfigurationForm)) as ConfigurationForm;
            if (configForm != null)
            {
                if (configForm.ShowDialog() == DialogResult.OK)
                {
                    // Configuration was saved, show restart message
                    var result = MessageBox.Show(
                        "Configuration has been saved successfully.\n\n" +
                        "Some changes may require restarting the application to take effect.\n\n" +
                        "Would you like to restart the application now?",
                        "Configuration Saved",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        Application.Restart();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error opening configuration form");
            MessageBox.Show($"Error opening configuration: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void menuExit_Click(object sender, EventArgs e)
    {
        this.Close();
    }

    private void menuAbout_Click(object sender, EventArgs e)
    {
        var aboutMessage = "Octopi Feasibility Checker\n" +
                          "Version 1.0.0\n\n" +
                          "A Windows Forms application for checking fibre feasibility\n" +
                          "using various API providers including the Octopi portal.\n\n" +
                          "Features:\n" +
                          "• Single and batch address checking\n" +
                          "• Multiple API provider support\n" +
                          "• Google Places address autocomplete\n" +
                          "• CSV import/export functionality\n" +
                          "• Intelligent caching and retry logic\n" +
                          "• Comprehensive error handling\n\n" +
                          "Developed with .NET 6 and Windows Forms\n" +
                          "© 2024 Octopi Feasibility Checker";

        MessageBox.Show(aboutMessage, "About Octopi Feasibility Checker", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private Icon? LoadIcon()
    {
        try
        {
            var iconPath = Path.Combine(Directory.GetCurrentDirectory(), "OCTOPI.png");
            if (File.Exists(iconPath))
            {
                using var bitmap = new Bitmap(iconPath);
                return Icon.FromHandle(bitmap.GetHicon());
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not load application icon");
        }
        return null;
    }

    protected override void OnFormClosing(FormClosingEventArgs e)
    {
        _cancellationTokenSource?.Cancel();
        _autocompleteTimer?.Dispose();
        HideAutocompleteSuggestions();
        base.OnFormClosing(e);
    }
}
