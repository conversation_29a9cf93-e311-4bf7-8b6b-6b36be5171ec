# Test APIs Script - 28East & Google Places
# Run this script to test the APIs before configuring in the application

Write-Host "🧪 Testing APIs for Octopi Feasibility Checker" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Test Google Places API
Write-Host "`n🌍 Testing Google Places API..." -ForegroundColor Yellow
$googleApiKey = "AIzaSyDEzt60rONb-C10rzDYhmK69IB2i16hALs"
$googleUrl = "https://maps.googleapis.com/maps/api/place/autocomplete/json?input=123+Main+Street+Cape+Town&key=$googleApiKey&types=address&components=country:za"

try {
    Write-Host "URL: $googleUrl" -ForegroundColor Gray
    $googleResponse = Invoke-RestMethod -Uri $googleUrl -Method GET -TimeoutSec 10
    
    if ($googleResponse.status -eq "OK") {
        Write-Host "✅ Google Places API: SUCCESS" -ForegroundColor Green
        Write-Host "   Predictions found: $($googleResponse.predictions.Count)" -ForegroundColor Green
        if ($googleResponse.predictions.Count -gt 0) {
            Write-Host "   First suggestion: $($googleResponse.predictions[0].description)" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ Google Places API: FAILED" -ForegroundColor Red
        Write-Host "   Status: $($googleResponse.status)" -ForegroundColor Red
        Write-Host "   Error: $($googleResponse.error_message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Google Places API: ERROR" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 28East Octopi API - Try different endpoints
Write-Host "`n🏢 Testing 28East Octopi API..." -ForegroundColor Yellow

$baseUrl = "https://octopi.28east.co.za"
$testAddress = "123 Main Street, Cape Town, 8001"
$endpoints = @(
    "/api/feasibility",
    "/api/v1/feasibility", 
    "/api/v2/feasibility",
    "/feasibility",
    "/check-feasibility",
    "/fno-check",
    "/coverage-check"
)

$headers = @{
    "Content-Type" = "application/json"
    "User-Agent" = "OctopiFeasibilityChecker/1.0"
}

foreach ($endpoint in $endpoints) {
    $fullUrl = "$baseUrl$endpoint"
    Write-Host "`n   Testing: $fullUrl" -ForegroundColor Gray
    
    try {
        # Try POST with JSON body
        $body = @{
            address = $testAddress
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri $fullUrl -Method POST -Body $body -Headers $headers -TimeoutSec 10
        Write-Host "   ✅ POST $endpoint: SUCCESS" -ForegroundColor Green
        Write-Host "   Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Green
        break
    } catch {
        Write-Host "   ❌ POST $endpoint: $($_.Exception.Message)" -ForegroundColor Red
        
        # Try GET method
        try {
            $getUrl = "$fullUrl?address=$([System.Web.HttpUtility]::UrlEncode($testAddress))"
            $response = Invoke-RestMethod -Uri $getUrl -Method GET -Headers $headers -TimeoutSec 10
            Write-Host "   ✅ GET $endpoint: SUCCESS" -ForegroundColor Green
            Write-Host "   Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Green
            break
        } catch {
            Write-Host "   ❌ GET $endpoint: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Test basic connectivity to 28East
Write-Host "`n🔗 Testing basic connectivity to 28East..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method GET -TimeoutSec 10
    Write-Host "✅ 28East website accessible" -ForegroundColor Green
    Write-Host "   Status: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor Green
    Write-Host "   Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor Green
} catch {
    Write-Host "❌ 28East website not accessible" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Summary and recommendations
Write-Host "`n📋 SUMMARY & RECOMMENDATIONS" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

Write-Host "`n🎯 Configuration Steps:" -ForegroundColor White
Write-Host "1. Run the Octopi Feasibility Checker application" -ForegroundColor Gray
Write-Host "2. Go to File → Configuration" -ForegroundColor Gray
Write-Host "3. Configure Google Places API with the provided key" -ForegroundColor Gray
Write-Host "4. Add 28East API provider with working endpoint (if found)" -ForegroundColor Gray
Write-Host "5. Test both providers using the 'Test Provider' button" -ForegroundColor Gray
Write-Host "6. Disable demo mode once APIs are working" -ForegroundColor Gray

Write-Host "`n🔧 If 28East API fails:" -ForegroundColor White
Write-Host "- Contact 28East for API documentation" -ForegroundColor Gray
Write-Host "- Ask for authentication requirements" -ForegroundColor Gray
Write-Host "- Request correct endpoint paths" -ForegroundColor Gray
Write-Host "- Check if API key or token is needed" -ForegroundColor Gray

Write-Host "`n✅ Ready to configure in application!" -ForegroundColor Green
