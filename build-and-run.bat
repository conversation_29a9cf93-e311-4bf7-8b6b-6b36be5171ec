@echo off
echo Building Octopi Feasibility Checker as standalone executable...
dotnet publish OctopiFeasibilityChecker.csproj --configuration Release --output ./publish

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Executable created in ./publish folder
    echo Starting application...
    .\publish\OctopiFeasibilityChecker.exe
) else (
    echo Build failed! Please check the errors above.
    pause
)
