{"format": 1, "restore": {"C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility\\OctopiFeasibilityChecker.csproj": {}}, "projects": {"C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility\\OctopiFeasibilityChecker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility\\OctopiFeasibilityChecker.csproj", "projectName": "OctopiFeasibilityChecker", "projectPath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility\\OctopiFeasibilityChecker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\MyDocuments\\ConradC\\OWN Builds\\Octopi-Feasibility\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"CsvHelper": {"target": "Package", "version": "[30.0.1, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}